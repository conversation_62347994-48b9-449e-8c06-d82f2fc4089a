from flask import Blueprint, request, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from models.models import User, db
from services.auth_service import AlgoDeltaAuthService
from datetime import datetime

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    email = data.get('email')
    password = data.get('password')
    domain_name = data.get('domain_name')

    if not all([email, password, domain_name]):
        return jsonify({'error': 'Missing required fields'}), 400

    # Initialize AlgoDelta auth service
    auth_service = AlgoDeltaAuthService(email, password, domain_name)
    success, result = auth_service.login()

    if not success:
        return jsonify({'error': 'AlgoDelta login failed', 'details': result}), 401

    # Check if user exists in our database
    user = User.query.filter_by(email=email).first()
    if not user:
        # Create new user if doesn't exist
        user = User(email=email, domain_name=domain_name)

    # Update user's tokens and login time
    user.auth_token = result['auth_token']
    user.refresh_token = result.get('refresh_token')
    user.last_login = datetime.utcnow()

    db.session.add(user)
    db.session.commit()

    login_user(user)

    return jsonify({
        'message': 'Login successful',
        'user': {
            'email': user.email,
            'domain_name': user.domain_name,
            'last_login': user.last_login.isoformat()
        }
    })

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    return jsonify({'message': 'Logout successful'})

@auth_bp.route('/refresh-token')
@login_required
def refresh_token():
    auth_service = AlgoDeltaAuthService()
    auth_service.refresh_token = current_user.refresh_token

    success, result = auth_service.refresh_session()
    if not success:
        return jsonify({'error': 'Token refresh failed', 'details': result}), 401

    # Update user's auth token
    current_user.auth_token = result['auth_token']
    db.session.commit()

    return jsonify({'message': 'Token refreshed successfully'})

@auth_bp.route('/status')
@login_required
def status():
    return jsonify({
        'authenticated': True,
        'user': {
            'email': current_user.email,
            'domain_name': current_user.domain_name,
            'last_login': current_user.last_login.isoformat()
        }
    })
import streamlit as st
import requests
import pandas as pd
from typing import Dict, <PERSON>ple
import concurrent.futures
import threading
import time
from datetime import datetime
import asyncio
import json

# Page configuration
st.set_page_config(
    page_title="⚡ AlgoFactory - Lightning PnL",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)

class AlgoDeltaAuthService:
    """Lightning-Fast AlgoDelta API Service with Connection Pooling"""
    BASE_URL = "https://bpapil1.algodelta.com/api/v1"

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        # Connection pooling for better performance
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=20,
            pool_maxsize=20,
            max_retries=3
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        self.auth_token = None
        self.last_pnl_data = {}  # Cache for PnL data
        self.last_update_time = None
    
    def login(self, email: str, password: str, domain_name: str = "algofactory.in") -> Tuple[bool, str]:
        """Login to AlgoDelta API"""
        url = f"{self.BASE_URL}/auth/login"
        payload = {"email": email, "password": password, "domain_name": domain_name}
        
        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()
            session_data = response.json()
            
            if 'token' in session_data:
                self.auth_token = session_data['token']
            elif 'data' in session_data and 'token' in session_data['data']:
                self.auth_token = session_data['data']['token']
            else:
                return False, 'Invalid response structure'
            
            self.session.headers.update({'Authorization': self.auth_token})
            return True, 'Login successful'
            
        except Exception as e:
            return False, str(e)
    
    def get_bridge_triggers(self, strategy_id: str) -> Tuple[bool, Dict]:
        """Get bridge triggers (PnL data) for a specific strategy"""
        url = f"{self.BASE_URL}/admin/bridge/getbridgetriggers"
        payload = {"strategy_id": strategy_id}
        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()
            return True, response.json()
        except Exception as e:
            return False, {'error': str(e)}

    def update_trading_flag(self, strategy_id: str, trading_flag: bool) -> Tuple[bool, Dict]:
        """Update trading flag for a specific strategy"""
        url = f"{self.BASE_URL}/admin/updatetradingflag"
        payload = {"strategy_id": strategy_id, "trading_flag": trading_flag}
        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()
            return True, response.json()
        except Exception as e:
            return False, {'error': str(e)}

    def get_all_pnl_data(self, strategy_ids: list, use_cache: bool = False) -> tuple:
        """Get PnL data for all strategies simultaneously with lightning speed"""
        start_time = time.time()

        def fetch_single_pnl(strategy_id):
            try:
                success, data = self.get_bridge_triggers(strategy_id)
                return {
                    'strategy_id': strategy_id,
                    'success': success,
                    'data': data,
                    'fetch_time': time.time()
                }
            except Exception as e:
                return {
                    'strategy_id': strategy_id,
                    'success': False,
                    'data': {'error': str(e)},
                    'fetch_time': time.time()
                }

        # Use ThreadPoolExecutor with optimized worker count for maximum speed
        max_workers = min(len(strategy_ids), 15)  # Optimal for most systems
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Create a mapping of future to strategy_id to maintain order
            future_to_strategy = {executor.submit(fetch_single_pnl, sid): sid for sid in strategy_ids}

            # Collect results maintaining the original order
            results = [None] * len(strategy_ids)
            for future in concurrent.futures.as_completed(future_to_strategy):
                result = future.result()
                strategy_id = result['strategy_id']
                # Find the index of this strategy in the original list
                index = strategy_ids.index(strategy_id)
                results[index] = result

        total_time = time.time() - start_time
        self.last_update_time = datetime.now()

        return results, total_time


    def check_and_execute_trading_actions(self, strategy_id: str, today_pnl: float, strategy_settings: dict) -> str:
        """Check if today's PnL hits stop loss or target and execute trading flag update"""
        stop_loss = strategy_settings.get('stop_loss', -500)
        target = strategy_settings.get('target', 1000)

        action_taken = ""

        # Check stop loss (negative number, so PnL should be <= stop_loss)
        if today_pnl <= stop_loss:
            success, response = self.update_trading_flag(strategy_id, False)
            if success:
                action_taken = f"🔴 STOP LOSS HIT! Trading disabled at ₹{today_pnl:,.2f}"
                st.session_state['auto_trading_actions'].append({
                    'strategy_id': strategy_id,
                    'action': 'stop_loss',
                    'pnl': today_pnl,
                    'threshold': stop_loss,
                    'time': datetime.now().strftime("%H:%M:%S")
                })
            else:
                action_taken = f"❌ Failed to disable trading: {response.get('error', 'Unknown error')}"

        # Check target (positive number, so PnL should be >= target)
        elif today_pnl >= target:
            success, response = self.update_trading_flag(strategy_id, False)
            if success:
                action_taken = f"🎯 TARGET HIT! Trading disabled at ₹{today_pnl:,.2f}"
                st.session_state['auto_trading_actions'].append({
                    'strategy_id': strategy_id,
                    'action': 'target',
                    'pnl': today_pnl,
                    'threshold': target,
                    'time': datetime.now().strftime("%H:%M:%S")
                })
            else:
                action_taken = f"❌ Failed to disable trading: {response.get('error', 'Unknown error')}"

        return action_taken



@st.dialog("⚙️ Edit Strategy Settings")
def single_strategy_edit_dialog():
    """Popup dialog for editing a single strategy's stop loss and target"""
    strategy_id = st.session_state.get('edit_strategy_id', '615')
    strategy_name = STRATEGY_NAMES.get(strategy_id, f"Strategy {strategy_id}")

    st.write(f"**Configure Stop Loss and Target for {strategy_name}**")
    st.caption("⚠️ Stop Loss must be negative, Target must be positive")

    # Get current values
    current_sl = st.session_state['strategy_settings'][strategy_id]['stop_loss']
    current_target = st.session_state['strategy_settings'][strategy_id]['target']

    # Create two columns for better layout
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("🔴 Stop Loss")
        new_sl = st.number_input(
            "Stop Loss Amount",
            value=float(current_sl),
            max_value=-1.0,
            step=50.0,
            key=f"single_sl_{strategy_id}",
            help="Enter negative value (e.g., -500)"
        )
        st.caption(f"Current: ₹{current_sl:,.0f}")

    with col2:
        st.subheader("🎯 Target")
        new_target = st.number_input(
            "Target Amount",
            value=float(current_target),
            min_value=1.0,
            step=50.0,
            key=f"single_target_{strategy_id}",
            help="Enter positive value (e.g., 1000)"
        )
        st.caption(f"Current: ₹{current_target:,.0f}")

    # Dialog buttons
    st.markdown("---")
    button_col1, button_col2, button_col3 = st.columns(3)

    with button_col1:
        if st.button("💾 Save Changes", type="primary", use_container_width=True):
            st.session_state['strategy_settings'][strategy_id]['stop_loss'] = new_sl
            st.session_state['strategy_settings'][strategy_id]['target'] = new_target
            st.session_state['show_single_edit_dialog'] = False
            st.session_state['edit_strategy_id'] = None  # Clear the selected strategy
            st.success(f"Settings saved for {strategy_name}!")
            st.rerun()

    with button_col2:
        if st.button("↩️ Reset to Default", use_container_width=True):
            default_settings = DEFAULT_STRATEGY_SETTINGS.get(strategy_id, {"stop_loss": -500, "target": 1000})
            st.session_state['strategy_settings'][strategy_id] = default_settings.copy()
            st.session_state['show_single_edit_dialog'] = False
            st.session_state['edit_strategy_id'] = None  # Clear the selected strategy
            st.success(f"Settings reset for {strategy_name}!")
            st.rerun()

    with button_col3:
        if st.button("❌ Cancel", use_container_width=True):
            st.session_state['show_single_edit_dialog'] = False
            st.session_state['edit_strategy_id'] = None  # Clear the selected strategy
            st.rerun()

# Predefined strategy IDs for fast PnL fetching
STRATEGY_IDS = ["615", "624", "625", "626", "634", "635", "637", "641", "675"]

# Strategy name mapping (fallback names if API doesn't provide names)
STRATEGY_NAMES = {
    "615": "Alpha Strategy 615",
    "624": "Beta Strategy 624",
    "625": "Gamma Strategy 625",
    "626": "Delta Strategy 626",
    "634": "Epsilon Strategy 634",
    "635": "Zeta Strategy 635",
    "637": "Eta Strategy 637",
    "641": "Theta Strategy 641",
    "675": "Iota Strategy 675"
}

# Default stop loss and target settings for each strategy
DEFAULT_STRATEGY_SETTINGS = {
    "615": {"stop_loss": -500, "target": 1000},
    "624": {"stop_loss": -500, "target": 1000},
    "625": {"stop_loss": -500, "target": 1000},
    "626": {"stop_loss": -500, "target": 1000},
    "634": {"stop_loss": -500, "target": 1000},
    "635": {"stop_loss": -500, "target": 1000},
    "637": {"stop_loss": -500, "target": 1000},
    "641": {"stop_loss": -500, "target": 1000},
    "675": {"stop_loss": -500, "target": 1000}
}

# Initialize session state for lightning-fast PnL with persistent login
if 'authenticated' not in st.session_state:
    st.session_state['authenticated'] = False
if 'auth_service' not in st.session_state:
    st.session_state['auth_service'] = None
if 'auth_token' not in st.session_state:
    st.session_state['auth_token'] = None
if 'user_credentials' not in st.session_state:
    st.session_state['user_credentials'] = None
if 'current_page' not in st.session_state:
    st.session_state['current_page'] = 'dashboard'
if 'auto_refresh' not in st.session_state:
    st.session_state['auto_refresh'] = True  # Default ON
if 'refresh_interval' not in st.session_state:
    st.session_state['refresh_interval'] = 5  # Fixed at 5 seconds
if 'last_pnl_data' not in st.session_state:
    st.session_state['last_pnl_data'] = None
if 'pnl_history' not in st.session_state:
    st.session_state['pnl_history'] = []
if 'strategy_settings' not in st.session_state:
    st.session_state['strategy_settings'] = DEFAULT_STRATEGY_SETTINGS.copy()
if 'auto_trading_actions' not in st.session_state:
    st.session_state['auto_trading_actions'] = []

import json
from pathlib import Path

# File-based session persistence
SESSION_FILE = Path(".streamlit_session.json")

def save_session_to_file(credentials, token):
    """Save session data to file"""
    try:
        session_data = {
            'credentials': credentials,
            'token': token,
            'timestamp': time.time()
        }
        with open(SESSION_FILE, 'w') as f:
            json.dump(session_data, f)
    except Exception as e:
        st.error(f"Failed to save session: {e}")

def load_session_from_file():
    """Load session data from file"""
    try:
        if SESSION_FILE.exists():
            with open(SESSION_FILE, 'r') as f:
                session_data = json.load(f)

            # Check if session is not too old (24 hours)
            if time.time() - session_data.get('timestamp', 0) < 86400:
                return session_data.get('credentials'), session_data.get('token')
        return None, None
    except Exception as e:
        return None, None

def clear_session_file():
    """Clear session file"""
    try:
        if SESSION_FILE.exists():
            SESSION_FILE.unlink()
    except Exception:
        pass

# Function to restore authentication
def restore_authentication():
    """Restore authentication from file"""
    if not st.session_state['authenticated']:
        credentials, token = load_session_from_file()

        if credentials and token:
            # Create new auth service and restore token
            auth_service = AlgoDeltaAuthService()
            auth_service.auth_token = token
            auth_service.session.headers.update({'Authorization': token})

            # Test if token is still valid
            try:
                success, _ = auth_service.get_bridge_triggers("615")
                if success:
                    st.session_state['authenticated'] = True
                    st.session_state['auth_service'] = auth_service
                    st.session_state['user_credentials'] = credentials
                    st.session_state['auth_token'] = token
                    return True
                else:
                    # Token expired, clear file
                    clear_session_file()
                    return False
            except:
                # Token expired, clear file
                clear_session_file()
                return False
    return False

# Try to restore authentication on app start
if not st.session_state['authenticated']:
    if restore_authentication():
        st.success("🔄 Session restored from previous login!")

def login_page():
    """Simple login page"""
    # Centered page heading
    st.markdown("<h2 style='text-align: center;'>🏭 AlgoFactory - Lightning PnL Dashboard</h2>", unsafe_allow_html=True)

    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown("<h3 style='text-align: center;'>🔐 Login</h3>", unsafe_allow_html=True)

        with st.form("login_form"):
            email = st.text_input("Email", placeholder="Enter your email")
            password = st.text_input("Password", placeholder="Enter your password", type="password")

            # Fixed domain name - no need for user input
            domain_name = "algofactory.in"

            if st.form_submit_button("Login", use_container_width=True):
                if email and password:
                    with st.spinner("Authenticating..."):
                        auth_service = AlgoDeltaAuthService()
                        success, message = auth_service.login(email, password, domain_name)

                        if success:
                            st.session_state['authenticated'] = True
                            st.session_state['auth_service'] = auth_service
                            # Store credentials and token for persistence
                            credentials = {
                                'email': email,
                                'password': password,
                                'domain_name': domain_name
                            }
                            st.session_state['user_credentials'] = credentials
                            st.session_state['auth_token'] = auth_service.auth_token

                            # Save to file for browser refresh persistence
                            save_session_to_file(credentials, auth_service.auth_token)

                            st.success("Login successful! (Session will persist on browser refresh)")
                            st.rerun()
                        else:
                            st.error(f"Login failed: {message}")
                else:
                    st.error("Please enter both email and password")

def dashboard_page():
    """Display lightning-fast main dashboard"""
    st.subheader("⚡ AlgoFactory Lightning Dashboard")
    st.markdown("---")

    # Welcome message
    st.write("**Welcome to Lightning-Fast PnL Dashboard**")
    st.info("⚡ Experience real-time PnL tracking with lightning speed!")

    # Dashboard metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("⚡ Total Strategies", len(STRATEGY_IDS), "Lightning Fast")
    with col2:
        st.metric("🚀 Active Strategies", "7", "Real-time")
    with col3:
        st.metric("👥 Total Users", "45", "5")
    with col4:
        st.metric("🟢 System Status", "Lightning Ready", "⚡")

    st.markdown("---")

    # Performance highlights
    st.write("**⚡ Performance Highlights**")
    perf_col1, perf_col2, perf_col3 = st.columns(3)

    with perf_col1:
        st.info("🚀 **Concurrent API Calls**\nFetch all strategies simultaneously")
    with perf_col2:
        st.info("⚡ **Sub-second Response**\nLightning-fast data retrieval")
    with perf_col3:
        st.info("🔄 **Real-time Updates**\nAuto-refresh every 5-30 seconds")

    st.markdown("---")

    # Quick info
    st.markdown("""
    **⚡ Lightning PnL Features**
    - **🚀 Concurrent Processing**: Fetch all strategy PnL data simultaneously
    - **⚡ Lightning Speed**: Sub-second response times with connection pooling
    - **🔄 Real-time Updates**: Auto-refresh with customizable intervals
    - **📊 Clean Interface**: Only Strategy ID, Name, Overall PnL, and Today's PnL

    👈 **Click "⚡ Lightning PnL" in the sidebar to experience the speed!**
    """)

    # Call to action
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("⚡ Go to Lightning PnL Dashboard", type="primary", use_container_width=True):
            st.session_state['current_page'] = 'pnl'
            st.rerun()

def pnl_page():
    """⚡ Lightning-Fast Real-Time PnL Dashboard"""
    # Header - clean and simple
    st.subheader("⚡ Lightning PnL Dashboard")

    # Show current auto-refresh status
    if st.session_state['auto_refresh']:
        st.info("🔄 Auto-refresh is ON (every 5 seconds) - Control it from the sidebar")
    else:
        st.info("⏸️ Auto-refresh is OFF - Enable it from the sidebar")

    st.markdown("---")

    auth_service = st.session_state['auth_service']

    # Simple control section
    col1, col2 = st.columns([1, 3])
    with col1:
        if st.button("⚡ Refresh PnL", type="primary"):
            st.rerun()
    with col2:
        # Performance metrics
        if st.session_state['last_pnl_data']:
            last_update = st.session_state['last_pnl_data'].get('update_time', 'Never')
            st.caption(f"Last Update: {last_update}")

    # Real-time status indicator
    status_placeholder = st.empty()
    with status_placeholder.container():
        st.info(f"⚡ Fetching PnL data for {len(STRATEGY_IDS)} strategies...")

    # Fetch PnL data
    with st.spinner("🚀 Loading PnL data..."):
        all_results, fetch_time = auth_service.get_all_pnl_data(STRATEGY_IDS)

    # Clear status and show performance metrics
    status_placeholder.empty()

    # Performance metrics
    perf_col1, perf_col2, perf_col3, perf_col4 = st.columns(4)
    with perf_col1:
        st.metric("⚡ Fetch Time", f"{fetch_time:.2f}s", f"-{max(0, 2.0-fetch_time):.1f}s")
    with perf_col2:
        st.metric("📊 Strategies", len(STRATEGY_IDS), "0")
    with perf_col3:
        successful_calls = sum(1 for r in all_results if r['success'])
        st.metric("✅ Success Rate", f"{successful_calls}/{len(all_results)}",
                 f"{(successful_calls/len(all_results)*100):.0f}%")
    with perf_col4:
        current_time = datetime.now().strftime("%H:%M:%S")
        st.metric("🕐 Last Update", current_time, "Live")

    st.markdown("---")

    # Process results with correct API structure extraction
    pnl_data = []
    total_today_pnl = 0.0
    total_overall_pnl = 0.0
    successful_strategies = 0

    for result in all_results:
        strategy_id = result['strategy_id']
        success = result['success']
        response_data = result['data']

        if success:

            # Extract data from the correct API response structure
            if 'data' in response_data and 'strategy_data' in response_data['data']:
                strategy_data = response_data['data']['strategy_data']

                # Extract strategy name from strategy_data
                strategy_name = strategy_data.get('strategy_name', STRATEGY_NAMES.get(strategy_id, f"Strategy {strategy_id}"))

                # Extract today's PnL
                today_pnl_raw = strategy_data.get('today_pnl', '0')
                today_pnl = 0.0
                try:
                    today_pnl = float(str(today_pnl_raw).replace(',', '').strip())
                    today_pnl_str = f"₹{today_pnl:,.2f}"
                    total_today_pnl += today_pnl
                except (ValueError, TypeError):
                    today_pnl_str = "₹0.00"

                # Extract overall PnL
                overall_pnl_raw = strategy_data.get('overall_pnl', '0')
                try:
                    overall_pnl = float(str(overall_pnl_raw).replace(',', '').strip())
                    overall_pnl_str = f"₹{overall_pnl:,.2f}"
                    total_overall_pnl += overall_pnl
                except (ValueError, TypeError):
                    overall_pnl_str = "₹0.00"

                # Get current trading flag status (assume true if not specified)
                trading_flag = response_data.get('trading_flag', True)
                trading_status = "🟢 ON" if trading_flag else "🔴 OFF"

                # Check stop loss and target for today's PnL
                strategy_settings = st.session_state['strategy_settings'].get(strategy_id, DEFAULT_STRATEGY_SETTINGS.get(strategy_id, {"stop_loss": -500, "target": 1000}))
                action_taken = ""

                # Only check if trading is currently ON and we have valid PnL data
                if trading_flag and today_pnl != 0:
                    action_taken = auth_service.check_and_execute_trading_actions(strategy_id, today_pnl, strategy_settings)
                    if action_taken:
                        trading_status = "🔴 OFF (Auto)"

                successful_strategies += 1
            else:
                # Fallback if structure is different
                strategy_name = STRATEGY_NAMES.get(strategy_id, f"Strategy {strategy_id}")
                today_pnl_str = "₹0.00"
                overall_pnl_str = "₹0.00"
                trading_status = "❓ Unknown"
                action_taken = ""

            # Get stop loss and target settings for this strategy
            strategy_settings = st.session_state['strategy_settings'].get(strategy_id, DEFAULT_STRATEGY_SETTINGS.get(strategy_id, {"stop_loss": -500, "target": 1000}))

            pnl_data.append({
                'Strategy ID': strategy_id,
                'Strategy Name': strategy_name,
                'Overall PnL': overall_pnl_str,
                'Today PnL': today_pnl_str,
                'Stop Loss': strategy_settings['stop_loss'],
                'Target': strategy_settings['target'],
                'Trading Flag': trading_status,
                'Auto Action': action_taken if action_taken else "✅ No Action"
            })
        else:
            # Get stop loss and target settings even for error cases
            strategy_settings = st.session_state['strategy_settings'].get(strategy_id, DEFAULT_STRATEGY_SETTINGS.get(strategy_id, {"stop_loss": -500, "target": 1000}))

            pnl_data.append({
                'Strategy ID': strategy_id,
                'Strategy Name': STRATEGY_NAMES.get(strategy_id, f"Strategy {strategy_id}"),
                'Overall PnL': f"❌ Error: {response_data.get('error', 'Unknown')}",
                'Today PnL': "❌ Error",
                'Stop Loss': strategy_settings['stop_loss'],
                'Target': strategy_settings['target'],
                'Trading Flag': "❌ Error",
                'Auto Action': "❌ Error"
            })



    # Display results with enhanced styling
    if pnl_data:
        st.success(f"✅ Lightning-fast PnL data loaded for {len(pnl_data)} strategies in {fetch_time:.2f} seconds!")

        # Store data for auto-refresh
        st.session_state['last_pnl_data'] = {
            'data': pnl_data,
            'update_time': datetime.now().strftime("%H:%M:%S"),
            'total_overall_pnl': total_overall_pnl,
            'total_today_pnl': total_today_pnl,
            'fetch_time': fetch_time
        }

        # Instructions for editing
        st.info("💡 **Click the 'Edit' button next to each strategy to modify its Stop Loss & Target values.**")

        # Create custom table with individual edit buttons
        # Table header
        header_cols = st.columns([1, 2, 1.5, 1.5, 1.5, 1.5, 1.5, 2, 1])
        headers = ["Strategy ID", "Strategy Name", "Overall PnL", "Today PnL", "Stop Loss", "Target", "Trading Flag", "Auto Action", "Edit"]

        for i, header in enumerate(headers):
            with header_cols[i]:
                st.write(f"**{header}**")

        st.markdown("---")

        # Table rows with individual edit buttons
        for item in pnl_data:
            row_cols = st.columns([1, 2, 1.5, 1.5, 1.5, 1.5, 1.5, 2, 1])

            with row_cols[0]:
                st.write(item['Strategy ID'])
            with row_cols[1]:
                st.write(item['Strategy Name'])
            with row_cols[2]:
                st.write(item['Overall PnL'])
            with row_cols[3]:
                st.write(item['Today PnL'])
            with row_cols[4]:
                st.write(f"₹{item['Stop Loss']:,.0f}")
            with row_cols[5]:
                st.write(f"₹{item['Target']:,.0f}")
            with row_cols[6]:
                st.write(item['Trading Flag'])
            with row_cols[7]:
                st.write(item['Auto Action'])
            with row_cols[8]:
                if st.button("⚙️ Edit", key=f"edit_{item['Strategy ID']}", type="secondary"):
                    st.session_state['edit_strategy_id'] = item['Strategy ID']
                    st.session_state['show_single_edit_dialog'] = True
                    st.rerun()

        # Popup Dialog for individual strategy editing
        if st.session_state.get('show_single_edit_dialog', False):
            single_strategy_edit_dialog()



        # Show recent auto trading actions
        if st.session_state['auto_trading_actions']:
            st.markdown("---")
            st.subheader("🤖 Recent Auto Trading Actions")
            recent_actions = st.session_state['auto_trading_actions'][-5:]  # Show last 5 actions
            for action in reversed(recent_actions):
                action_type = "🔴 STOP LOSS" if action['action'] == 'stop_loss' else "🎯 TARGET"
                st.info(f"{action['time']} - Strategy {action['strategy_id']}: {action_type} hit at ₹{action['pnl']:,.2f} (Threshold: ₹{action['threshold']:,.2f})")

        # Auto-refresh functionality with editing protection
        if st.session_state['auto_refresh']:
            # Add a small delay to prevent interference with editing
            placeholder = st.empty()
            for remaining in range(st.session_state['refresh_interval'], 0, -1):
                placeholder.caption(f"⏱️ Auto-refresh in {remaining} seconds... (editing is safe)")
                time.sleep(1)
            placeholder.empty()
            st.rerun()


    else:
        st.warning("⚠️ No PnL data available - Check your connection and strategy IDs")

def main_page():
    """Main page with lightning-fast sidebar navigation"""
    # Sidebar navigation
    with st.sidebar:
        st.title("⚡ AlgoFactory")
        st.caption("Lightning-Fast PnL Dashboard")
        st.markdown("---")

        # Navigation buttons
        if st.button("🏠 Dashboard", use_container_width=True,
                    type="primary" if st.session_state['current_page'] == 'dashboard' else "secondary"):
            st.session_state['current_page'] = 'dashboard'
            st.rerun()

        if st.button("⚡ Lightning PnL", use_container_width=True,
                    type="primary" if st.session_state['current_page'] == 'pnl' else "secondary"):
            st.session_state['current_page'] = 'pnl'
            st.rerun()

        st.markdown("---")

        # Auto-refresh controls in navbar - simplified
        st.subheader("🔄 Auto Refresh")
        auto_refresh = st.toggle("Enable Auto Refresh", value=st.session_state['auto_refresh'], key="navbar_auto_refresh")
        st.session_state['auto_refresh'] = auto_refresh

        if auto_refresh:
            st.caption("⚡ Refreshing every 5 seconds")
        else:
            st.caption("⏸️ Auto-refresh is OFF")

        st.markdown("---")

        # PnL Status
        if st.session_state['current_page'] == 'pnl' and st.session_state['last_pnl_data']:
            st.subheader("📊 PnL Status")
            last_update = st.session_state['last_pnl_data']['update_time']
            fetch_time = st.session_state['last_pnl_data']['fetch_time']
            st.caption(f"🕐 Last update: {last_update}")
            st.caption(f"⚡ Fetch time: {fetch_time:.2f}s")

        # Session status
        st.markdown("---")
        st.subheader("🔐 Session Status")
        if st.session_state['user_credentials']:
            email = st.session_state['user_credentials']['email']
            st.success(f"✅ Logged in as: {email}")
            st.caption("🔄 Session persists on page refresh")
        else:
            st.info("ℹ️ No persistent session")

        st.markdown("---")

        # Logout button
        if st.button("🚪 Logout", use_container_width=True, type="secondary"):
            st.session_state['authenticated'] = False
            st.session_state['auth_service'] = None
            st.session_state['auth_token'] = None
            st.session_state['user_credentials'] = None
            st.session_state['current_page'] = 'dashboard'
            st.session_state['auto_refresh'] = False

            # Clear session file
            clear_session_file()

            st.success("Logged out successfully!")
            st.rerun()

    # Main content based on current page
    if st.session_state['current_page'] == 'dashboard':
        dashboard_page()
    elif st.session_state['current_page'] == 'pnl':
        pnl_page()

# Main application logic
def main():
    if not st.session_state['authenticated']:
        login_page()
    else:
        main_page()

if __name__ == "__main__":
    main()

from app import create_app
from extensions import db
from models.strategy_info import StrategyInfo, StrategyRecord, StrategyOrder
from models.broker_data import BrokerData
from models.user_data import UserData

def init_db():
    app = create_app()
    with app.app_context():
        # Create all database tables
        db.create_all()
        print('Database tables created successfully!')

if __name__ == '__main__':
    init_db()
from datetime import datetime
from extensions import db
from pytz import timezone

ist = timezone('Asia/Kolkata')

class BrokerData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    broker_id = db.Column(db.String(255), nullable=False)
    broker_name = db.Column(db.String(255), nullable=False)
    user_id = db.Column(db.String(255), nullable=False)
    user_name = db.Column(db.String(255), nullable=False)
    trading_flag = db.Column(db.Bo<PERSON>, default=False)
    broker_username = db.Column(db.String(255))
    parent_id = db.Column(db.Integer)
    nic_name = db.Column(db.String(255))
    demat_name = db.Column(db.String(255))
    broker_flag = db.Column(db.Boolean, default=False)
    pnl = db.Column(db.Float, default=0.0)
    positions = db.Column(db.Integer, default=0)
    margin = db.Column(db.Float, default=0.0)
    broker_data = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(ist), onupdate=lambda: datetime.now(ist))
    
    def __repr__(self):
        return f'<BrokerData {self.demat_name} (ID: {self.broker_id})>'
    
    @property
    def to_dict(self):
        return {
            'id': self.id,
            'broker_id': self.broker_id,
            'broker_name': self.broker_name,
            'user_id': self.user_id,
            'user_name': self.user_name,
            'trading_flag': self.trading_flag,
            'broker_username': self.broker_username,
            'parent_id': self.parent_id,
            'nic_name': self.nic_name,
            'demat_name': self.demat_name,
            'broker_flag': self.broker_flag,
            'pnl': self.pnl,
            'positions': self.positions,
            'margin': self.margin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
import mysql.connector
import logging
from datetime import datetime
from pytz import timezone
from tabulate import tabulate
import time
import os

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

class MySQLReader:
    def __init__(self, host, user, password, database, port=3306):
        self.config = {
            'host': 'srv1498.hstgr.io',
            'user': 'u352667016_algofactory',
            'password': 'Apapap92119211#',
            'database': 'u352667016_algofactory',
            'port': port
        }
        self.connection = None
        self.cursor = None

    def connect(self):
        try:
            self.connection = mysql.connector.connect(**self.config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info('Successfully connected to MySQL database')
            return True
        except mysql.connector.Error as err:
            logger.error(f'Error connecting to MySQL database: {err}')
            return False

    def disconnect(self):
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            logger.info('Successfully disconnected from MySQL database')
        except mysql.connector.Error as err:
            logger.error(f'Error disconnecting from MySQL database: {err}')

    def execute_query(self, query, params=None):
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect():
                    return None

            self.cursor.execute(query, params)
            results = self.cursor.fetchall()
            logger.info(f'Successfully executed query: {query}')
            return results

        except mysql.connector.Error as err:
            logger.error(f'Error executing query: {err}')
            return None

        except Exception as e:
            logger.error(f'Unexpected error: {str(e)}')
            return None

    def get_server_variables(self):
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect():
                    return None

            # Query important server variables related to limits
            variables_query = """
            SHOW VARIABLES WHERE Variable_name IN (
                'max_connections',
                'max_user_connections',
                'max_connect_errors',
                'connect_timeout',
                'wait_timeout',
                'interactive_timeout',
                'max_allowed_packet'
            )
            """
            self.cursor.execute(variables_query)
            variables = self.cursor.fetchall()

            # Get current connection status
            status_query = """
            SHOW STATUS WHERE Variable_name IN (
                'Max_used_connections',
                'Threads_connected',
                'Connections',
                'Aborted_connects',
                'Aborted_clients'
            )
            """
            self.cursor.execute(status_query)
            status = self.cursor.fetchall()

            return {'variables': variables, 'status': status}

        except mysql.connector.Error as err:
            logger.error(f'Error getting server variables: {err}')
            return None

        except Exception as e:
            logger.error(f'Unexpected error: {str(e)}')
            return None

    def get_server_variables(self):
        try:
            if not self.connection or not self.connection.is_connected():
                if not self.connect():
                    return None

            # Query important server variables related to limits
            variables_query = """
            SHOW VARIABLES WHERE Variable_name IN (
                'max_connections',
                'max_user_connections',
                'max_connect_errors',
                'connect_timeout',
                'wait_timeout',
                'interactive_timeout',
                'max_allowed_packet'
            )
            """
            self.cursor.execute(variables_query)
            variables = self.cursor.fetchall()

            # Get current connection status
            status_query = """
            SHOW STATUS WHERE Variable_name IN (
                'Max_used_connections',
                'Threads_connected',
                'Connections',
                'Aborted_connects',
                'Aborted_clients'
            )
            """
            self.cursor.execute(status_query)
            status = self.cursor.fetchall()

            # Print the server variables and status
            print('\nMySQL Server Variables:')
            print(tabulate(variables, headers=['Variable', 'Value'], tablefmt='grid'))
            
            print('\nMySQL Server Status:')
            print(tabulate(status, headers=['Variable', 'Value'], tablefmt='grid'))

            return {'variables': variables, 'status': status}

        except mysql.connector.Error as err:
            logger.error(f'Error getting server variables: {err}')
            return None

        except Exception as e:
            logger.error(f'Unexpected error: {str(e)}')
            return None

def main():
    try:
        # Create MySQL reader instance with hardcoded credentials
        mysql_reader = MySQLReader(
            host='srv1498.hstgr.io',
            user='u352667016_algofactory',
            password='Apapap92119211#',
            database='u352667016_algofactory'
        )

        # Connect to database
        if mysql_reader.connect():
            # First check server variables and limits
            mysql_reader.get_server_variables()
            print('\nPress Ctrl+C to exit\n')
            # Query to fetch specific user columns including trail_by and lock_profit
            query = "SELECT id, email, name, target_amount, stoploss_amount, trail_by, lock_profit FROM users"
            results = mysql_reader.execute_query(query)

            while True:
                # Clear screen and move cursor to top
                print('\033[2J\033[H')
                
                # Execute query and get results
                results = mysql_reader.execute_query(query)
                
                if results:
                    # Get current time in IST
                    current_time = datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S %Z')
                    print(f'Last Updated: {current_time}\n')
                    
                    # Prepare table data
                    table_data = []
                    for row in results:
                        table_data.append([
                            row.get('id', 'N/A'),
                            row.get('email', 'N/A'),
                            row.get('name', 'N/A'),
                            row.get('target_amount', 'N/A'),
                            row.get('stoploss_amount', 'N/A'),
                            row.get('trail_by', 'N/A'),
                            row.get('lock_profit', 'N/A')
                        ])
                    
                    # Print table using tabulate
                    print(tabulate(table_data,
                                 headers=['ID', 'Email', 'Name', 'Target Amount', 'Stoploss Amount', 'Trail By', 'Lock Profit'],
                                 tablefmt='grid'))
                else:
                    print('\nNo users found or error occurred')
                
                # Wait for 1 second before next refresh
                time.sleep(5)

    except Exception as e:
        logger.error(f'Error in main: {str(e)}')

    finally:
        # Always disconnect from database
        if mysql_reader:
            mysql_reader.disconnect()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print('\nOperation cancelled by user')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
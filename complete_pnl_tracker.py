import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import requests
import json
from datetime import datetime

class CompletePnLTracker:
    def __init__(self, root):
        self.root = root
        self.root.title("Complete PnL Tracker - Broker ID: 3167")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # API Configuration
        self.base_url = "https://bpapil1.algodelta.com/api/v1"
        self.broker_id = 3167
        self.email = "<EMAIL>"
        self.password = "Ap9211###"
        self.domain_name = "algofactory.in"
        
        # Authentication
        self.auth_token = None
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        
        # Initialize variables
        self.is_running = False
        self.tracking_thread = None

        # Trading parameters
        self.target_amount = tk.DoubleVar(value=500.0)
        self.stoploss_amount = tk.DoubleVar(value=-500.0)
        self.trailing_sl_trigger = tk.DoubleVar(value=200.0)
        self.trailing_sl_step = tk.DoubleVar(value=50.0)
        self.auto_squareoff_enabled = tk.BooleanVar(value=True)

        # Trailing SL tracking
        self.trailing_sl_level = None
        self.highest_pnl = 0.0
        self.last_pnl = 0.0

        # Create GUI elements
        self.create_widgets()
        
    def create_widgets(self):
        """Create and arrange GUI widgets"""
        # Title
        title_label = tk.Label(
            self.root, 
            text="Complete PnL Tracker - Broker ID: 3167", 
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#333333'
        )
        title_label.pack(pady=10)
        
        # Control frame
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(pady=10)
        
        # Login button
        self.login_button = tk.Button(
            control_frame,
            text="Login",
            command=self.login,
            bg='#2196F3',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=20,
            pady=5
        )
        self.login_button.pack(side=tk.LEFT, padx=5)
        
        # Start/Stop buttons
        self.start_button = tk.Button(
            control_frame,
            text="Start Tracking",
            command=self.start_tracking,
            bg='#4CAF50',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=20,
            pady=5,
            state=tk.DISABLED
        )
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = tk.Button(
            control_frame,
            text="Stop Tracking",
            command=self.stop_tracking,
            bg='#f44336',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=20,
            pady=5,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Status label
        self.status_label = tk.Label(
            self.root,
            text="Status: Not Logged In",
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#666666'
        )
        self.status_label.pack(pady=5)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)

        # PnL Summary Tab
        self.create_pnl_tab()

        # Settings Tab
        self.create_settings_tab()

        # Positions/Trades Tab
        self.create_positions_tab()

        # All Data Tab
        self.create_data_tab()

        # Log Tab
        self.create_log_tab()
        
    def create_pnl_tab(self):
        """Create PnL summary tab"""
        pnl_frame = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(pnl_frame, text="PnL Summary")
        
        # Create grid layout for data
        data_grid = tk.Frame(pnl_frame, bg='#f0f0f0')
        data_grid.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Configure grid weights
        for i in range(3):
            data_grid.columnconfigure(i, weight=1)
        
        # Timestamp
        tk.Label(data_grid, text="Last Updated:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.timestamp_label = tk.Label(data_grid, text="--", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.timestamp_label.grid(row=0, column=1, columnspan=2, sticky='w', padx=5, pady=5)
        
        # Demat Name
        tk.Label(data_grid, text="Demat Name:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.demat_label = tk.Label(data_grid, text="--", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.demat_label.grid(row=1, column=1, columnspan=2, sticky='w', padx=5, pady=5)
        
        # PNL (highlighted)
        tk.Label(data_grid, text="PNL:", font=("Arial", 14, "bold"), bg='#f0f0f0').grid(row=2, column=0, sticky='w', padx=5, pady=15)
        self.pnl_label = tk.Label(
            data_grid, 
            text="--", 
            font=("Arial", 24, "bold"), 
            bg='#f0f0f0', 
            fg='#333333'
        )
        self.pnl_label.grid(row=2, column=1, columnspan=2, sticky='w', padx=5, pady=15)
        
        # Margin
        tk.Label(data_grid, text="Margin:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=3, column=0, sticky='w', padx=5, pady=5)
        self.margin_label = tk.Label(data_grid, text="--", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.margin_label.grid(row=3, column=1, columnspan=2, sticky='w', padx=5, pady=5)
        
        # Broker Flag
        tk.Label(data_grid, text="Broker Flag:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=4, column=0, sticky='w', padx=5, pady=5)
        self.broker_flag_label = tk.Label(data_grid, text="--", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.broker_flag_label.grid(row=4, column=1, columnspan=2, sticky='w', padx=5, pady=5)

    def create_settings_tab(self):
        """Create settings tab for trading parameters"""
        settings_frame = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(settings_frame, text="Settings")

        # Main container with padding
        main_container = tk.Frame(settings_frame, bg='#f0f0f0')
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Auto Square-off Enable/Disable
        auto_frame = tk.LabelFrame(main_container, text="Auto Square-off Control", font=("Arial", 12, "bold"), bg='#f0f0f0', fg='#333333')
        auto_frame.pack(fill=tk.X, pady=(0, 20))

        self.auto_checkbox = tk.Checkbutton(
            auto_frame,
            text="Enable Auto Square-off",
            variable=self.auto_squareoff_enabled,
            font=("Arial", 11),
            bg='#f0f0f0',
            fg='#333333'
        )
        self.auto_checkbox.pack(pady=10)

        # Trading Parameters Frame
        params_frame = tk.LabelFrame(main_container, text="Trading Parameters", font=("Arial", 12, "bold"), bg='#f0f0f0', fg='#333333')
        params_frame.pack(fill=tk.X, pady=(0, 20))

        # Configure grid
        for i in range(3):
            params_frame.columnconfigure(i, weight=1)

        # Target Amount
        tk.Label(params_frame, text="Target Amount (₹):", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=0, column=0, sticky='w', padx=10, pady=10)
        self.target_entry = tk.Entry(params_frame, textvariable=self.target_amount, font=("Arial", 10), width=15)
        self.target_entry.grid(row=0, column=1, padx=10, pady=10)
        self.target_status = tk.Label(params_frame, text="Not Triggered", font=("Arial", 9), bg='#f0f0f0', fg='#666666')
        self.target_status.grid(row=0, column=2, padx=10, pady=10)

        # Stop Loss Amount
        tk.Label(params_frame, text="Stop Loss Amount (₹):", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=1, column=0, sticky='w', padx=10, pady=10)
        self.stoploss_entry = tk.Entry(params_frame, textvariable=self.stoploss_amount, font=("Arial", 10), width=15)
        self.stoploss_entry.grid(row=1, column=1, padx=10, pady=10)
        self.stoploss_status = tk.Label(params_frame, text="Not Triggered", font=("Arial", 9), bg='#f0f0f0', fg='#666666')
        self.stoploss_status.grid(row=1, column=2, padx=10, pady=10)

        # Trailing SL Frame
        trailing_frame = tk.LabelFrame(main_container, text="Trailing Stop Loss", font=("Arial", 12, "bold"), bg='#f0f0f0', fg='#333333')
        trailing_frame.pack(fill=tk.X, pady=(0, 20))

        # Configure grid
        for i in range(3):
            trailing_frame.columnconfigure(i, weight=1)

        # Trailing SL Trigger
        tk.Label(trailing_frame, text="Trigger at Profit (₹):", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=0, column=0, sticky='w', padx=10, pady=10)
        self.trailing_trigger_entry = tk.Entry(trailing_frame, textvariable=self.trailing_sl_trigger, font=("Arial", 10), width=15)
        self.trailing_trigger_entry.grid(row=0, column=1, padx=10, pady=10)
        self.trailing_trigger_status = tk.Label(trailing_frame, text="Not Active", font=("Arial", 9), bg='#f0f0f0', fg='#666666')
        self.trailing_trigger_status.grid(row=0, column=2, padx=10, pady=10)

        # Trailing SL Step
        tk.Label(trailing_frame, text="Trail Step (₹):", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=1, column=0, sticky='w', padx=10, pady=10)
        self.trailing_step_entry = tk.Entry(trailing_frame, textvariable=self.trailing_sl_step, font=("Arial", 10), width=15)
        self.trailing_step_entry.grid(row=1, column=1, padx=10, pady=10)
        self.trailing_step_status = tk.Label(trailing_frame, text="--", font=("Arial", 9), bg='#f0f0f0', fg='#666666')
        self.trailing_step_status.grid(row=1, column=2, padx=10, pady=10)

        # Current Trailing Level
        tk.Label(trailing_frame, text="Current Trail Level (₹):", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=2, column=0, sticky='w', padx=10, pady=10)
        self.current_trail_label = tk.Label(trailing_frame, text="Not Set", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.current_trail_label.grid(row=2, column=1, padx=10, pady=10)

        # Control Buttons
        button_frame = tk.Frame(main_container, bg='#f0f0f0')
        button_frame.pack(fill=tk.X, pady=10)

        # Reset Trailing SL
        reset_btn = tk.Button(
            button_frame,
            text="Reset Trailing SL",
            command=self.reset_trailing_sl,
            bg='#ff9800',
            fg='white',
            font=("Arial", 10, "bold"),
            padx=15,
            pady=5
        )
        reset_btn.pack(side=tk.LEFT, padx=5)

        # Test Alerts
        test_btn = tk.Button(
            button_frame,
            text="Test Alert",
            command=self.test_alert,
            bg='#9C27B0',
            fg='white',
            font=("Arial", 10, "bold"),
            padx=15,
            pady=5
        )
        test_btn.pack(side=tk.LEFT, padx=5)

        # Current Status Frame
        status_frame = tk.LabelFrame(main_container, text="Current Status", font=("Arial", 12, "bold"), bg='#f0f0f0', fg='#333333')
        status_frame.pack(fill=tk.X)

        # Status labels
        self.highest_pnl_label = tk.Label(status_frame, text="Highest P&L: ₹0.00", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.highest_pnl_label.pack(pady=5)

        self.last_check_label = tk.Label(status_frame, text="Last Check: --", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.last_check_label.pack(pady=5)

    def create_positions_tab(self):
        """Create positions/trades tab"""
        positions_frame = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(positions_frame, text="Positions & Trades")

        # Create treeview for positions
        tree_frame = tk.Frame(positions_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Define columns
        columns = ('Symbol', 'Type', 'Qty', 'P&L', 'LTP', 'Avg Price', 'Trans', 'Square Off')

        self.positions_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # Define headings
        self.positions_tree.heading('Symbol', text='Symbol')
        self.positions_tree.heading('Type', text='Type')
        self.positions_tree.heading('Qty', text='Qty')
        self.positions_tree.heading('P&L', text='P&L')
        self.positions_tree.heading('LTP', text='LTP')
        self.positions_tree.heading('Avg Price', text='Avg Price')
        self.positions_tree.heading('Trans', text='Trans')
        self.positions_tree.heading('Square Off', text='Square Off')

        # Configure column widths
        self.positions_tree.column('Symbol', width=150)
        self.positions_tree.column('Type', width=80)
        self.positions_tree.column('Qty', width=80)
        self.positions_tree.column('P&L', width=100)
        self.positions_tree.column('LTP', width=80)
        self.positions_tree.column('Avg Price', width=80)
        self.positions_tree.column('Trans', width=80)
        self.positions_tree.column('Square Off', width=100)

        # Add scrollbar
        positions_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=positions_scrollbar.set)

        self.positions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        positions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_data_tab(self):
        """Create all data tab"""
        data_frame = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(data_frame, text="All Data")
        
        # Create text widget with scrollbar for all data
        data_scroll_frame = tk.Frame(data_frame)
        data_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.data_text = tk.Text(
            data_scroll_frame,
            font=("Courier", 9),
            bg='#ffffff',
            fg='#333333'
        )
        
        data_scrollbar = ttk.Scrollbar(data_scroll_frame, orient=tk.VERTICAL, command=self.data_text.yview)
        self.data_text.configure(yscrollcommand=data_scrollbar.set)
        
        self.data_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        data_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_log_tab(self):
        """Create log tab"""
        log_frame = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(log_frame, text="Activity Log")
        
        # Log text widget with scrollbar
        log_scroll_frame = tk.Frame(log_frame)
        log_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = tk.Text(
            log_scroll_frame,
            font=("Courier", 9),
            bg='#ffffff',
            fg='#333333'
        )
        
        log_scrollbar = ttk.Scrollbar(log_scroll_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def log_message(self, message):
        """Add message to log display"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # Auto-scroll to bottom
        
        # Limit log size (keep last 100 lines)
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", f"{len(lines)-100}.0")
            
    def login(self):
        """Login to AlgoDelta API"""
        try:
            self.log_message("Attempting login...")
            self.status_label.config(text="Status: Logging in...", fg='#ff9800')
            
            url = f"{self.base_url}/auth/login"
            payload = {
                "email": self.email,
                "password": self.password,
                "domain_name": self.domain_name
            }
            
            response = self.session.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                session_data = response.json()
                
                # Extract token
                if 'token' in session_data:
                    self.auth_token = session_data['token']
                elif 'data' in session_data and 'token' in session_data['data']:
                    self.auth_token = session_data['data']['token']
                else:
                    self.log_message(f"Login failed: No token in response")
                    self.status_label.config(text="Status: Login Failed", fg='#f44336')
                    return
                
                # Update session headers
                self.session.headers.update({'Authorization': self.auth_token})
                
                self.log_message("Login successful!")
                self.status_label.config(text="Status: Logged In", fg='#4CAF50')
                
                # Enable start button
                self.start_button.config(state=tk.NORMAL)
                self.login_button.config(state=tk.DISABLED)
                
                # Verify token
                self.verify_token()
                
            else:
                self.log_message(f"Login failed: HTTP {response.status_code}")
                self.status_label.config(text="Status: Login Failed", fg='#f44336')
                
        except Exception as e:
            self.log_message(f"Login error: {str(e)}")
            self.status_label.config(text="Status: Login Error", fg='#f44336')
            
    def verify_token(self):
        """Verify the authentication token"""
        try:
            url = f"{self.base_url}/auth/verify"
            payload = {"token": self.auth_token}
            
            response = self.session.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                self.log_message("Token verified successfully")
            else:
                self.log_message(f"Token verification failed: HTTP {response.status_code}")
                
        except Exception as e:
            self.log_message(f"Token verification error: {str(e)}")
            
    def start_tracking(self):
        """Start PnL tracking"""
        if self.is_running or not self.auth_token:
            return
            
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_label.config(text="Status: Tracking", fg='#4CAF50')
        
        # Start tracking in separate thread
        self.tracking_thread = threading.Thread(target=self.track_pnl, daemon=True)
        self.tracking_thread.start()
        
        self.log_message("PnL tracking started")
        
    def stop_tracking(self):
        """Stop PnL tracking"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="Status: Logged In", fg='#4CAF50')
        
        self.log_message("PnL tracking stopped")
        
    def fetch_broker_data(self):
        """Fetch broker data from API"""
        try:
            url = f"{self.base_url}/broker/getbrokerinfo"
            payload = {"broker_id": self.broker_id}
            
            response = self.session.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    return True, data['data']
                else:
                    return False, f"No data in response: {data}"
            else:
                return False, f"HTTP {response.status_code}: {response.text}"
                
        except Exception as e:
            return False, f"Request error: {str(e)}"
            
    def track_pnl(self):
        """Main tracking loop"""
        while self.is_running:
            try:
                success, result = self.fetch_broker_data()
                
                if success:
                    # Update GUI with new data
                    self.root.after(0, lambda: self.update_display(result))
                else:
                    # Log error
                    self.root.after(0, lambda: self.log_message(f"Error: {result}"))
                
                time.sleep(1)  # Wait 1 second
                
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"Tracking error: {str(e)}"))
                time.sleep(1)
                
    def update_display(self, broker_data):
        """Update GUI display with broker data"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Update PnL summary tab
        self.timestamp_label.config(text=current_time)
        self.demat_label.config(text=broker_data.get('demat_name', 'N/A'))
        self.margin_label.config(text=str(broker_data.get('margin', 'N/A')))
        self.broker_flag_label.config(text=str(broker_data.get('broker_flag', 'N/A')))
        
        # Update PNL with color coding
        pnl = broker_data.get('pnl', 'N/A')
        self.pnl_label.config(text=str(pnl))
        
        if pnl != 'N/A':
            try:
                pnl_value = float(pnl)
                if pnl_value > 0:
                    self.pnl_label.config(fg='#4CAF50')  # Green for profit
                elif pnl_value < 0:
                    self.pnl_label.config(fg='#f44336')  # Red for loss
                else:
                    self.pnl_label.config(fg='#333333')  # Black for zero
            except ValueError:
                self.pnl_label.config(fg='#333333')
        
        # Update positions tab
        self.update_positions_table(broker_data)

        # Check trading conditions
        if pnl != 'N/A':
            try:
                pnl_value = float(pnl)
                self.check_trading_conditions(pnl_value)
            except ValueError:
                pass

        # Update all data tab
        self.data_text.delete(1.0, tk.END)
        formatted_data = json.dumps(broker_data, indent=2)
        self.data_text.insert(tk.END, formatted_data)

        self.log_message(f"PnL: {pnl}")

    def update_positions_table(self, broker_data):
        """Update positions table with broker data"""
        # Clear existing items
        for item in self.positions_tree.get_children():
            self.positions_tree.delete(item)

        # Check if positions data exists
        positions = broker_data.get('positions', [])
        if not positions:
            # Check for other possible keys that might contain position data
            for key in ['trades', 'holdings', 'portfolio', 'position_data']:
                if key in broker_data:
                    positions = broker_data[key]
                    break

        if positions and isinstance(positions, list):
            for position in positions:
                # Extract position data with fallback values
                symbol = position.get('symbol', position.get('tradingsymbol', 'N/A'))
                pos_type = position.get('type', position.get('product', 'N/A'))
                qty = position.get('qty', position.get('quantity', 0))
                pnl = position.get('pnl', position.get('unrealised', position.get('realised', 0)))
                ltp = position.get('ltp', position.get('last_price', 0))
                avg_price = position.get('avg_price', position.get('average_price', 0))
                trans = position.get('trans', position.get('transaction_type', 'N/A'))

                # Color code P&L
                pnl_text = str(pnl)
                if isinstance(pnl, (int, float)):
                    if pnl > 0:
                        pnl_text = f"+{pnl}"
                    elif pnl < 0:
                        pnl_text = str(pnl)

                # Insert row
                item = self.positions_tree.insert('', 'end', values=(
                    symbol, pos_type, qty, pnl_text, ltp, avg_price, trans, 'Square OFF'
                ))

                # Color code the P&L column
                if isinstance(pnl, (int, float)):
                    if pnl > 0:
                        self.positions_tree.set(item, 'P&L', f"+{pnl}")
                    elif pnl < 0:
                        self.positions_tree.set(item, 'P&L', str(pnl))
        else:
            # If no positions found, show a message
            self.positions_tree.insert('', 'end', values=(
                'No positions data found', '', '', '', '', '', '', ''
            ))

            # Log what keys are available in the broker_data
            available_keys = list(broker_data.keys()) if isinstance(broker_data, dict) else []
            self.log_message(f"Available data keys: {available_keys}")

    def check_trading_conditions(self, current_pnl):
        """Check if any trading conditions are met"""
        if not self.auto_squareoff_enabled.get():
            return

        self.last_pnl = current_pnl

        # Update highest PnL
        if current_pnl > self.highest_pnl:
            self.highest_pnl = current_pnl
            self.highest_pnl_label.config(text=f"Highest P&L: ₹{self.highest_pnl:.2f}")

        # Update last check time
        current_time = datetime.now().strftime('%H:%M:%S')
        self.last_check_label.config(text=f"Last Check: {current_time}")

        # Check Target
        if current_pnl >= self.target_amount.get():
            self.trigger_target_alert(current_pnl)
            return

        # Check Stop Loss
        if current_pnl <= self.stoploss_amount.get():
            self.trigger_stoploss_alert(current_pnl)
            return

        # Check Trailing Stop Loss
        self.check_trailing_sl(current_pnl)

    def check_trailing_sl(self, current_pnl):
        """Check and update trailing stop loss"""
        trigger_level = self.trailing_sl_trigger.get()
        trail_step = self.trailing_sl_step.get()

        # Check if trailing SL should be activated
        if current_pnl >= trigger_level and self.trailing_sl_level is None:
            self.trailing_sl_level = current_pnl - trail_step
            self.current_trail_label.config(text=f"₹{self.trailing_sl_level:.2f}")
            self.trailing_trigger_status.config(text="Active", fg='#4CAF50')
            self.log_message(f"Trailing SL activated at ₹{self.trailing_sl_level:.2f}")

        # Update trailing SL if active
        elif self.trailing_sl_level is not None:
            new_trail_level = current_pnl - trail_step

            # Update trailing level if profit increased
            if new_trail_level > self.trailing_sl_level:
                self.trailing_sl_level = new_trail_level
                self.current_trail_label.config(text=f"₹{self.trailing_sl_level:.2f}")
                self.trailing_step_status.config(text=f"Updated", fg='#4CAF50')
                self.log_message(f"Trailing SL updated to ₹{self.trailing_sl_level:.2f}")

            # Check if trailing SL is hit
            if current_pnl <= self.trailing_sl_level:
                self.trigger_trailing_sl_alert(current_pnl, self.trailing_sl_level)

    def trigger_target_alert(self, current_pnl):
        """Trigger target reached alert"""
        self.target_status.config(text="TRIGGERED!", fg='#4CAF50', font=("Arial", 9, "bold"))
        self.log_message(f"TARGET REACHED! P&L: ₹{current_pnl:.2f}")

        # Show popup
        messagebox.showinfo(
            "🎯 TARGET REACHED!",
            f"Target of ₹{self.target_amount.get():.2f} reached!\n\n"
            f"Current P&L: ₹{current_pnl:.2f}\n\n"
            f"Auto Square-off would be executed here."
        )

        # Stop tracking after target
        self.stop_tracking()

    def trigger_stoploss_alert(self, current_pnl):
        """Trigger stop loss alert"""
        self.stoploss_status.config(text="TRIGGERED!", fg='#f44336', font=("Arial", 9, "bold"))
        self.log_message(f"STOP LOSS HIT! P&L: ₹{current_pnl:.2f}")

        # Show popup
        messagebox.showwarning(
            "🛑 STOP LOSS HIT!",
            f"Stop loss of ₹{self.stoploss_amount.get():.2f} hit!\n\n"
            f"Current P&L: ₹{current_pnl:.2f}\n\n"
            f"Auto Square-off would be executed here."
        )

        # Stop tracking after stop loss
        self.stop_tracking()

    def trigger_trailing_sl_alert(self, current_pnl, trail_level):
        """Trigger trailing stop loss alert"""
        self.trailing_trigger_status.config(text="TRIGGERED!", fg='#f44336', font=("Arial", 9, "bold"))
        self.log_message(f"TRAILING SL HIT! P&L: ₹{current_pnl:.2f}, Trail Level: ₹{trail_level:.2f}")

        # Show popup
        messagebox.showwarning(
            "📉 TRAILING STOP LOSS HIT!",
            f"Trailing stop loss hit!\n\n"
            f"Current P&L: ₹{current_pnl:.2f}\n"
            f"Trail Level: ₹{trail_level:.2f}\n\n"
            f"Auto Square-off would be executed here."
        )

        # Stop tracking after trailing SL
        self.stop_tracking()

    def reset_trailing_sl(self):
        """Reset trailing stop loss"""
        self.trailing_sl_level = None
        self.highest_pnl = 0.0
        self.current_trail_label.config(text="Not Set")
        self.trailing_trigger_status.config(text="Not Active", fg='#666666')
        self.trailing_step_status.config(text="--", fg='#666666')
        self.highest_pnl_label.config(text="Highest P&L: ₹0.00")
        self.log_message("Trailing SL reset")

    def test_alert(self):
        """Test alert popup"""
        messagebox.showinfo(
            "🧪 Test Alert",
            "This is a test alert popup!\n\n"
            "All alerts are working correctly."
        )

def main():
    root = tk.Tk()
    app = CompletePnLTracker(root)
    
    # Handle window close
    def on_closing():
        if app.is_running:
            app.stop_tracking()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == '__main__':
    main()

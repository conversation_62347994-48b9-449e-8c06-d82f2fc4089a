import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import requests
import json
from datetime import datetime

class SimplePnLTracker:
    def __init__(self, root):
        self.root = root
        self.root.title("PnL Tracker - Broker ID: 3167")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # API Configuration
        self.api_url = "https://bpapil1.algodelta.com/api/v1/broker/getbrokerinfo"
        self.broker_id = 3167
        self.access_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.Dqfd_XKnsdmCJ4P-YIRRORrHG5DfRFMifgImlhbk19o"
        
        # Initialize variables
        self.is_running = False
        self.tracking_thread = None
        
        # Create GUI elements
        self.create_widgets()
        
    def create_widgets(self):
        """Create and arrange GUI widgets"""
        # Title
        title_label = tk.Label(
            self.root, 
            text="PnL Tracker - Broker ID: 3167", 
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#333333'
        )
        title_label.pack(pady=10)
        
        # Control frame
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(pady=10)
        
        # Start/Stop buttons
        self.start_button = tk.Button(
            control_frame,
            text="Start Tracking",
            command=self.start_tracking,
            bg='#4CAF50',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=20,
            pady=5
        )
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = tk.Button(
            control_frame,
            text="Stop Tracking",
            command=self.stop_tracking,
            bg='#f44336',
            fg='white',
            font=("Arial", 12, "bold"),
            padx=20,
            pady=5,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Status label
        self.status_label = tk.Label(
            self.root,
            text="Status: Stopped",
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#666666'
        )
        self.status_label.pack(pady=5)
        
        # Data display frame
        data_frame = tk.LabelFrame(
            self.root,
            text="Broker Information",
            font=("Arial", 12, "bold"),
            bg='#f0f0f0',
            fg='#333333',
            padx=10,
            pady=10
        )
        data_frame.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
        
        # Create data labels
        self.create_data_labels(data_frame)
        
        # Log display
        log_frame = tk.LabelFrame(
            self.root,
            text="Activity Log",
            font=("Arial", 10, "bold"),
            bg='#f0f0f0',
            fg='#333333'
        )
        log_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        # Log text widget with scrollbar
        log_scroll_frame = tk.Frame(log_frame)
        log_scroll_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(
            log_scroll_frame,
            height=8,
            font=("Courier", 9),
            bg='#ffffff',
            fg='#333333'
        )
        
        log_scrollbar = ttk.Scrollbar(log_scroll_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_data_labels(self, parent):
        """Create labels for displaying broker data"""
        # Create grid layout for data
        data_grid = tk.Frame(parent, bg='#f0f0f0')
        data_grid.pack(fill=tk.BOTH, expand=True)
        
        # Configure grid weights
        for i in range(3):
            data_grid.columnconfigure(i, weight=1)
        
        # Timestamp
        tk.Label(data_grid, text="Last Updated:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.timestamp_label = tk.Label(data_grid, text="--", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.timestamp_label.grid(row=0, column=1, columnspan=2, sticky='w', padx=5, pady=5)
        
        # Demat Name
        tk.Label(data_grid, text="Demat Name:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.demat_label = tk.Label(data_grid, text="--", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.demat_label.grid(row=1, column=1, columnspan=2, sticky='w', padx=5, pady=5)
        
        # PNL (highlighted)
        tk.Label(data_grid, text="PNL:", font=("Arial", 12, "bold"), bg='#f0f0f0').grid(row=2, column=0, sticky='w', padx=5, pady=10)
        self.pnl_label = tk.Label(
            data_grid, 
            text="--", 
            font=("Arial", 20, "bold"), 
            bg='#f0f0f0', 
            fg='#333333'
        )
        self.pnl_label.grid(row=2, column=1, columnspan=2, sticky='w', padx=5, pady=10)
        
        # Margin
        tk.Label(data_grid, text="Margin:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=3, column=0, sticky='w', padx=5, pady=5)
        self.margin_label = tk.Label(data_grid, text="--", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.margin_label.grid(row=3, column=1, columnspan=2, sticky='w', padx=5, pady=5)
        
        # Broker Flag
        tk.Label(data_grid, text="Broker Flag:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=4, column=0, sticky='w', padx=5, pady=5)
        self.broker_flag_label = tk.Label(data_grid, text="--", font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.broker_flag_label.grid(row=4, column=1, columnspan=2, sticky='w', padx=5, pady=5)
        
        # Broker ID
        tk.Label(data_grid, text="Broker ID:", font=("Arial", 10, "bold"), bg='#f0f0f0').grid(row=5, column=0, sticky='w', padx=5, pady=5)
        self.broker_id_label = tk.Label(data_grid, text=str(self.broker_id), font=("Arial", 10), bg='#f0f0f0', fg='#666666')
        self.broker_id_label.grid(row=5, column=1, columnspan=2, sticky='w', padx=5, pady=5)
        
    def log_message(self, message):
        """Add message to log display"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # Auto-scroll to bottom
        
        # Limit log size (keep last 100 lines)
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", f"{len(lines)-100}.0")
            
    def start_tracking(self):
        """Start PnL tracking"""
        if self.is_running:
            return
            
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_label.config(text="Status: Running", fg='#4CAF50')
        
        # Start tracking in separate thread
        self.tracking_thread = threading.Thread(target=self.track_pnl, daemon=True)
        self.tracking_thread.start()
        
        self.log_message("PnL tracking started")
        
    def stop_tracking(self):
        """Stop PnL tracking"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="Status: Stopped", fg='#666666')
        
        self.log_message("PnL tracking stopped")
        
    def fetch_broker_data(self):
        """Fetch broker data from API"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.access_token}'
            }
            
            payload = {
                'broker_id': self.broker_id
            }
            
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    return True, data['data']
                else:
                    return False, f"No data in response: {data}"
            else:
                return False, f"HTTP {response.status_code}: {response.text}"
                
        except requests.exceptions.RequestException as e:
            return False, f"Request error: {str(e)}"
        except json.JSONDecodeError as e:
            return False, f"JSON decode error: {str(e)}"
        except Exception as e:
            return False, f"Unexpected error: {str(e)}"
            
    def track_pnl(self):
        """Main tracking loop"""
        while self.is_running:
            try:
                success, result = self.fetch_broker_data()
                
                if success:
                    # Update GUI with new data
                    self.root.after(0, lambda: self.update_display(result))
                else:
                    # Log error
                    self.root.after(0, lambda: self.log_message(f"Error: {result}"))
                
                time.sleep(1)  # Wait 1 second
                
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"Tracking error: {str(e)}"))
                time.sleep(1)
                
    def update_display(self, broker_data):
        """Update GUI display with broker data"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Update labels
        self.timestamp_label.config(text=current_time)
        self.demat_label.config(text=broker_data.get('demat_name', 'N/A'))
        self.margin_label.config(text=str(broker_data.get('margin', 'N/A')))
        self.broker_flag_label.config(text=str(broker_data.get('broker_flag', 'N/A')))
        
        # Update PNL with color coding
        pnl = broker_data.get('pnl', 'N/A')
        self.pnl_label.config(text=str(pnl))
        
        if pnl != 'N/A':
            try:
                pnl_value = float(pnl)
                if pnl_value > 0:
                    self.pnl_label.config(fg='#4CAF50')  # Green for profit
                elif pnl_value < 0:
                    self.pnl_label.config(fg='#f44336')  # Red for loss
                else:
                    self.pnl_label.config(fg='#333333')  # Black for zero
            except ValueError:
                self.pnl_label.config(fg='#333333')
                
        self.log_message(f"PnL: {pnl}")

def main():
    root = tk.Tk()
    app = SimplePnLTracker(root)
    
    # Handle window close
    def on_closing():
        if app.is_running:
            app.stop_tracking()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == '__main__':
    main()

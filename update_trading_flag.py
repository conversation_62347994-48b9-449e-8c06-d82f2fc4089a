from services.auth_service import AlgoDeltaAuthService
import json
import logging
from datetime import datetime
from pytz import timezone
from app import create_app

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def update_trading_flag(strategy_id, trading_flag):
    auth_service = AlgoDeltaAuthService()
    
    try:
        # Login to get authentication token
        success, result = auth_service.login()
        if not success:
            error_msg = result.get('error', 'Unknown error')
            logger.error(f'Authentication failed: {error_msg}')
            return False, error_msg
        
        # Prepare payload for the API request
        payload = {
            'strategy_id': strategy_id,
            'trading_flag': trading_flag
        }
        
        # Make API request to update trading flag
        success, data = auth_service.make_api_request(
            'POST',
            'admin/updatetradingflag',
            data=payload
        )
        
        if success:
            logger.info(f'Successfully updated trading flag for strategy {strategy_id} to {trading_flag}')
            return True, 'Trading flag updated successfully'
        else:
            error_msg = data.get('error', 'Unknown error') if isinstance(data, dict) else 'Unknown error'
            logger.error(f'Failed to update trading flag: {error_msg}')
            return False, error_msg
    
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Unexpected error: {error_msg}')
        return False, error_msg

if __name__ == '__main__':
    try:
        app = create_app()
        with app.app_context():
            # Example usage
            strategy_id = 637  # Replace with your strategy ID
            trading_flag = True  # Replace with desired trading flag value
            
            success, message = update_trading_flag(strategy_id, trading_flag)
            if success:
                print(f'\nSuccess: {message}')
            else:
                print(f'\nError: {message}')
    except KeyboardInterrupt:
        print('\nStopping trading flag update...')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
from services.auth_service import AlgoDeltaAuthService
import time
import json
from datetime import datetime, timedelta
from app import create_app
import logging
from pytz import timezone
import os

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def delete_users():
    app = create_app()
    last_login_time = None
    token_expiry = timedelta(hours=1)
    max_retries = 5
    retry_count = 0
    backoff_time = 5
    max_backoff_time = 120

    with app.app_context():
        auth_service = AlgoDeltaAuthService()
        current_time = datetime.now()
        try:
            # Check and refresh token if needed
            if not auth_service.auth_token or \
               (last_login_time and current_time - last_login_time >= token_expiry) or \
               (hasattr(auth_service, 'token_expiry') and auth_service.token_expiry and current_time >= auth_service.token_expiry):
                logger.info('Token expired or missing - attempting to refresh/login')
                
                # Try token refresh first if we have a refresh token
                if hasattr(auth_service, 'refresh_token') and auth_service.refresh_token:
                    logger.info('Attempting token refresh')
                    success, result = auth_service.refresh_session()
                    if not success:
                        logger.warning('Token refresh failed, falling back to login')
                        success, result = auth_service.login()
                else:
                    success, result = auth_service.login()
                
                if not success:
                    error_msg = result.get('error', 'Unknown error')
                    logger.error(f'Authentication failed: {error_msg}')
                    return False
                
                last_login_time = current_time

            # Fetch all users first
            success, data = auth_service.make_api_request('GET', 'admin/getusers')
            
            if not success or not isinstance(data, dict) or 'data' not in data:
                logger.error('Failed to fetch users')
                return False

            users_to_delete = []
            protected_count = 0
            for user in data['data']:
                try:
                    user_id = int(user.get('id', 0))
                    email = user.get('email', '').lower()
                    if 'sharetradesoftware.com' not in email:
                        users_to_delete.append(user_id)
                    else:
                        protected_count += 1
                        logger.info(f'Skipping protected user {user_id} with email {email}')
                except (ValueError, TypeError):
                    continue

            logger.info(f'Found {len(users_to_delete)} users to delete and {protected_count} protected users')

            # Delete users
            deleted_count = 0
            for user_id in users_to_delete:
                try:
                    success, result = auth_service.make_api_request(
                        'POST',
                        'admin/deleteuser',
                        data={'user_id': user_id}
                    )

                    if success:
                        logger.info(f'Successfully deleted user {user_id}')
                        deleted_count += 1
                    else:
                        logger.warning(f'Failed to delete user {user_id}: {result.get("error", "Unknown error")}')

                    # Add a small delay between deletions
                    time.sleep(0.5)

                except Exception as e:
                    logger.error(f'Error deleting user {user_id}: {str(e)}')
                    continue

            logger.info(f'Deletion complete. Successfully deleted {deleted_count} out of {len(users_to_delete)} users. Protected {protected_count} users.')
            return True

        except Exception as e:
            logger.error(f'Unexpected error: {str(e)}')
            return False

if __name__ == '__main__':
    try:
        success = delete_users()
        if success:
            print('\nUser deletion process completed.')
        else:
            print('\nUser deletion process failed.')
    except KeyboardInterrupt:
        print('\nOperation cancelled by user.')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
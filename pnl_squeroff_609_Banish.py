from services.auth_service import AlgoDeltaAuthService
import json
import logging
from datetime import datetime
from pytz import timezone
from app import create_app
from tabulate import tabulate
import time
import os
import requests
from mysql_data_reader import get_user_trading_params

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging with unique name
logger = logging.getLogger('pnl_squeroff_609_Banish')
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def get_broker_info(broker_id):
    auth_service = AlgoDeltaAuthService()
    
    # Define strategy IDs
    strategy_ids = [646, 647, 649]
    
    # Enable trading flags at startup
    for strategy_id in strategy_ids:
        strategy_payload = {
            'strategy_id': strategy_id,
            'trading_flag': True
        }
        success, result = auth_service.make_api_request(
            'POST',
            'admin/updatetradingflag',
            data=strategy_payload
        )
        if success:
            logger.info(f'Trading flag enabled for strategy {strategy_id}')
        else:
            error_msg = result.get('error', 'Unknown error')
            logger.error(f'Failed to enable trading flag for strategy {strategy_id}: {error_msg}')
            return

    
    # Webhook configuration
    webhook_urls = [
        "https://bpapil1.algodelta.com/api/v1/admin/bridge/webhook/646/58954851425847595478",
        "https://bpapil1.algodelta.com/api/v1/admin/bridge/webhook/647/15489458769548762548",
        "https://bpapil1.algodelta.com/api/v1/admin/bridge/webhook/649/69548547862514578625"
    ]
    headers = {'Content-Type': 'text/plain'}
    webhook_payload = "0"  # Initial webhook payload
    
    try:
        # Login to get authentication token
        success, result = auth_service.login()
        if not success:
            error_msg = result.get('error', 'Unknown error')
            logger.error(f'Authentication failed: {error_msg}')
            return
        
        while True:
            try:
                # Make API request to get broker info
                payload = {'broker_id': broker_id}
                success, data = auth_service.make_api_request(
                    'POST',
                    'broker/getbrokerinfo',
                    data=payload
                )
                
                if success and isinstance(data, dict) and 'data' in data:
                    broker_data = data['data']
                    
                    # Get dynamic trading parameters from MySQL
                    trading_params = get_user_trading_params('<EMAIL>')
                    if not trading_params:
                        logger.error('Failed to fetch trading parameters, using default values')
                        trading_params = {
                            'target_amount': 500,
                            'stoploss_amount': -500,
                            'trail_by': 0,
                            'lock_profit': 0
                        }
                    
                    # Check PNL thresholds and send webhook notification
                    pnl = float(broker_data.get('pnl', 0))
                    
                    # Initialize trailing stop level if not set
                    if not hasattr(get_broker_info, 'trailing_stop_level'):
                        get_broker_info.trailing_stop_level = None
                    
                    # Check if PNL has reached lock profit level
                    if trading_params['lock_profit'] > 0 and pnl >= trading_params['lock_profit']:
                        # Initialize or update trailing stop level
                        if get_broker_info.trailing_stop_level is None:
                            get_broker_info.trailing_stop_level = pnl - trading_params['trail_by']
                            logger.info(f'Initialized trailing stop at {get_broker_info.trailing_stop_level}')
                        elif pnl - get_broker_info.trailing_stop_level >= trading_params['trail_by']:
                            # Update trailing stop when profit increases by trail_by amount
                            get_broker_info.trailing_stop_level = pnl - trading_params['trail_by']
                            logger.info(f'Updated trailing stop to {get_broker_info.trailing_stop_level}')
                        
                        # Check if profit has fallen below trailing stop
                        if pnl <= get_broker_info.trailing_stop_level:
                            logger.info(f'PNL {pnl} hit trailing stop at {get_broker_info.trailing_stop_level}')
                            try:
                                # Send webhook notifications to all endpoints
                                for webhook_url in webhook_urls:
                                    response = requests.post(webhook_url, headers=headers, data=webhook_payload)
                                    if response.status_code == 200:
                                        logger.info(f'Webhook notification sent successfully to {webhook_url} for PNL {pnl}. Status Code: {response.status_code}')
                                        print(f'\nWebhook Response Content: {response.text}')
                                    else:
                                        logger.error(f'Webhook request failed for {webhook_url} with status code {response.status_code}')
                                        print(f'\nWebhook Error Response: {response.text}')
                                logger.info('All webhook notifications processed')
                                
                                # Update trading flags for all strategies
                                for strategy_id in strategy_ids:
                                    strategy_payload = {
                                        'strategy_id': strategy_id,
                                        'trading_flag': False
                                    }
                                    success, result = auth_service.make_api_request(
                                        'POST',
                                        'admin/updatetradingflag',
                                        data=strategy_payload
                                    )
                                    if success:
                                        logger.info(f'Trading flag updated successfully for strategy {strategy_id}')
                                    else:
                                        error_msg = result.get('error', 'Unknown error')
                                        logger.error(f'Failed to update trading flag for strategy {strategy_id}: {error_msg}')
                                return
                            except Exception as e:
                                logger.error(f'Failed to send webhook notification: {str(e)}')
                                print(f'\nWebhook Error: {str(e)}')
                                return
                    # Check regular stop loss and target levels
                    elif pnl <= trading_params['stoploss_amount'] or pnl >= trading_params['target_amount']:
                        try:
                            # Send webhook notifications to all endpoints
                            for webhook_url in webhook_urls:
                                response = requests.post(webhook_url, headers=headers, data=webhook_payload)
                                if response.status_code == 200:
                                    logger.info(f'Webhook notification sent successfully to {webhook_url} for PNL {pnl}. Status Code: {response.status_code}')
                                    print(f'\nWebhook Response Content: {response.text}')
                                else:
                                    logger.error(f'Webhook request failed for {webhook_url} with status code {response.status_code}')
                                    print(f'\nWebhook Error Response: {response.text}')
                            logger.info('All webhook notifications processed')
                            
                            # Update trading flags for all strategies
                            for strategy_id in strategy_ids:
                                strategy_payload = {
                                    'strategy_id': strategy_id,
                                    'trading_flag': False
                                }
                                success, result = auth_service.make_api_request(
                                    'POST',
                                    'admin/updatetradingflag',
                                    data=strategy_payload
                                )
                                if success:
                                    logger.info(f'Trading flag updated successfully for strategy {strategy_id}')
                                else:
                                    error_msg = result.get('error', 'Unknown error')
                                    logger.error(f'Failed to update trading flag for strategy {strategy_id}: {error_msg}')
                            return
                        except Exception as e:
                            logger.error(f'Failed to send webhook notification: {str(e)}')
                            print(f'\nWebhook Error: {str(e)}')
                            return
                    # Get current time in IST
                    current_time = datetime.now(ist).strftime('%Y-%m-%d %H:%M:%S %Z')
                    
                    # Clear screen and move cursor to top
                    print('\033[2J\033[H')
                    
                    # Extract required information
                    table_data = [[
                        current_time,
                        broker_data.get('demat_name', 'N/A'),
                        broker_data.get('pnl', 'N/A'),
                        broker_data.get('margin', 'N/A'),
                        broker_data.get('broker_flag', 'N/A'),
                        broker_data.get('broker_id', 'N/A')
                    ]]
                    
                    # Print table
                    print(tabulate(table_data, 
                                 headers=['Timestamp', 'Demat Name', 'PNL', 'Margin', 'Broker Flag', 'Broker ID'],
                                 tablefmt='grid'))
                else:
                    error_msg = data.get('error', 'Unknown error') if isinstance(data, dict) else 'Unknown error'
                    logger.error(f'Failed to fetch broker info: {error_msg}')
                
                time.sleep(1)  # Wait for 1 second before next update
                
            except Exception as e:
                logger.error(f'Error fetching broker info: {str(e)}')
                time.sleep(1)  # Wait before retrying
    
    except Exception as e:
        logger.error(f'Unexpected error: {str(e)}')

if __name__ == '__main__':
    try:
        app = create_app()
        with app.app_context():
            broker_id = 609  # The broker ID you want to fetch information for
            get_broker_info(broker_id)
    except KeyboardInterrupt:
        print('\nStopping broker info fetch...')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
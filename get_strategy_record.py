from services.auth_service import AlgoDeltaAuthService
import json
from datetime import datetime
from app import create_app
import logging
from pytz import timezone
import os

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def get_strategy_record(strategy_id="637"):
    app = create_app()

    with app.app_context():
        auth_service = AlgoDeltaAuthService()
        try:
            # Login first
            success, result = auth_service.login()
            if not success:
                error_msg = result.get('error', 'Unknown error')
                logger.error(f'Authentication failed: {error_msg}')
                return False

            # Make API request to get strategy record
            success, data = auth_service.make_api_request(
                'POST',
                'admin/getstrategyrecord',
                data={'strategy_id': strategy_id}
            )

            if not success:
                logger.error('Failed to fetch strategy record')
                return False

            # Create data directory if it doesn't exist
            data_dir = os.path.join(os.path.dirname(__file__), 'data')
            os.makedirs(data_dir, exist_ok=True)

            # Save to JSON file with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'strategy_record_{strategy_id}_{timestamp}.json'
            filepath = os.path.join(data_dir, filename)

            with open(filepath, 'w') as f:
                json.dump(data, f, indent=4)

            logger.info(f'Successfully saved strategy record to {filename}')
            return True

        except Exception as e:
            logger.error(f'Unexpected error: {str(e)}')
            return False

if __name__ == '__main__':
    try:
        success = get_strategy_record()
        if success:
            print('\nStrategy record fetching process completed.')
        else:
            print('\nStrategy record fetching process failed.')
    except KeyboardInterrupt:
        print('\nOperation cancelled by user.')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from models.models import ApiData, ApiLog, db
from services.auth_service import AlgoDeltaAuthService
from datetime import datetime

api_bp = Blueprint('api', __name__)

def get_auth_service():
    """Helper function to get authenticated AlgoDelta service instance."""
    auth_service = AlgoDeltaAuthService()
    auth_service.auth_token = current_user.auth_token
    auth_service.refresh_token = current_user.refresh_token
    return auth_service

def log_api_call(method, endpoint, request_data, response_data, status_code):
    """Helper function to log API calls."""
    log = ApiLog(
        method=method,
        endpoint=endpoint,
        request_data=request_data,
        response_data=response_data,
        status_code=status_code,
        user_id=current_user.id
    )
    db.session.add(log)
    db.session.commit()

@api_bp.route('/proxy/<path:endpoint>', methods=['GET', 'POST', 'PUT', 'DELETE'])
@login_required
def proxy_api(endpoint):
    """Generic proxy endpoint for AlgoDelta API calls."""
    auth_service = get_auth_service()
    method = request.method
    data = request.get_json() if request.is_json else None

    success, result = auth_service.make_api_request(method, endpoint, data)
    status_code = 200 if success else 400

    # Log the API call
    log_api_call(method, endpoint, data, result, status_code)

    if success:
        # Store successful API responses
        api_data = ApiData(
            endpoint=endpoint,
            response_data=result,
            status='success',
            user_id=current_user.id
        )
        db.session.add(api_data)
        db.session.commit()

    return jsonify(result), status_code

@api_bp.route('/data', methods=['GET'])
@login_required
def get_api_data():
    """Get stored API data for the current user."""
    endpoint = request.args.get('endpoint')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    query = ApiData.query.filter_by(user_id=current_user.id)

    if endpoint:
        query = query.filter_by(endpoint=endpoint)
    if start_date:
        query = query.filter(ApiData.created_at >= start_date)
    if end_date:
        query = query.filter(ApiData.created_at <= end_date)

    data = query.order_by(ApiData.created_at.desc()).all()
    return jsonify([
        {
            'id': item.id,
            'endpoint': item.endpoint,
            'response_data': item.response_data,
            'status': item.status,
            'created_at': item.created_at.isoformat()
        } for item in data
    ])

@api_bp.route('/logs', methods=['GET'])
@login_required
def get_api_logs():
    """Get API call logs for the current user."""
    endpoint = request.args.get('endpoint')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    query = ApiLog.query.filter_by(user_id=current_user.id)

    if endpoint:
        query = query.filter_by(endpoint=endpoint)
    if start_date:
        query = query.filter(ApiLog.created_at >= start_date)
    if end_date:
        query = query.filter(ApiLog.created_at <= end_date)

    logs = query.order_by(ApiLog.created_at.desc()).all()
    return jsonify([
        {
            'id': log.id,
            'method': log.method,
            'endpoint': log.endpoint,
            'request_data': log.request_data,
            'response_data': log.response_data,
            'status_code': log.status_code,
            'created_at': log.created_at.isoformat()
        } for log in logs
    ])
from services.auth_service import AlgoDeltaAuthService
import json
import logging
from datetime import datetime
from pytz import timezone
from app import create_app

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def update_all_strategies_flag(trading_flag):
    auth_service = AlgoDeltaAuthService()
    
    try:
        # Login to get authentication token
        success, result = auth_service.login()
        if not success:
            error_msg = result.get('error', 'Unknown error')
            logger.error(f'Authentication failed: {error_msg}')
            return False, error_msg
        
        # Fetch all strategies
        success, data = auth_service.make_api_request('GET', 'admin/getstrategies')
        
        if not success or not isinstance(data, dict) or 'data' not in data:
            error_msg = data.get('error', 'Unknown error') if isinstance(data, dict) else 'Unknown error'
            logger.error(f'Failed to fetch strategies: {error_msg}')
            return False, error_msg
        
        strategy_list = data['data']
        total_strategies = len(strategy_list)
        updated_count = 0
        failed_count = 0
        failed_strategies = []
        
        logger.info(f'Found {total_strategies} strategies to update')
        
        for strategy in strategy_list:
            strategy_info = strategy.get('strategy_info', {})
            strategy_id = strategy_info.get('id')
            
            if not strategy_id:
                logger.warning('Strategy ID not found in strategy info')
                continue
            
            # Prepare payload for the API request
            payload = {
                'strategy_id': strategy_id,
                'trading_flag': trading_flag
            }
            
            # Make API request to update trading flag
            success, update_data = auth_service.make_api_request(
                'POST',
                'admin/updatetradingflag',
                data=payload
            )
            
            if success:
                logger.info(f'Successfully updated trading flag for strategy {strategy_id} to {trading_flag}')
                updated_count += 1
            else:
                error_msg = update_data.get('error', 'Unknown error') if isinstance(update_data, dict) else 'Unknown error'
                logger.error(f'Failed to update trading flag for strategy {strategy_id}: {error_msg}')
                failed_count += 1
                failed_strategies.append(strategy_id)
        
        # Prepare summary message
        summary = f'\nUpdate Summary:\n'
        summary += f'Total strategies processed: {total_strategies}\n'
        summary += f'Successfully updated: {updated_count}\n'
        summary += f'Failed updates: {failed_count}\n'
        
        if failed_strategies:
            summary += f'Failed strategy IDs: {failed_strategies}\n'
        
        logger.info(summary)
        return True, summary
    
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Unexpected error: {error_msg}')
        return False, error_msg

if __name__ == '__main__':
    try:
        app = create_app()
        with app.app_context():
            # Set trading flag to False to disable all strategies
            trading_flag = True
            
            success, message = update_all_strategies_flag(trading_flag)
            if success:
                print(f'\nSuccess: {message}')
            else:
                print(f'\nError: {message}')
    except KeyboardInterrupt:
        print('\nStopping trading flag update...')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
from datetime import datetime
from extensions import db
from pytz import timezone

ist = timezone('Asia/Kolkata')

class UserData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(255), nullable=False)
    org_id = db.Column(db.String(255))
    name = db.Column(db.String(255))
    email = db.Column(db.String(255))
    mobile = db.Column(db.String(20))
    nic_name = db.Column(db.String(255))
    status = db.Column(db.String(50))
    trading_flag = db.Column(db.<PERSON>, default=False)
    is_admin = db.Column(db.<PERSON>, default=False)
    is_super_admin = db.Column(db.Bo<PERSON>, default=False)
    user_data = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(ist), onupdate=lambda: datetime.now(ist))
    
    def __repr__(self):
        return f'<UserData {self.nic_name} (ID: {self.user_id})>'
    
    @property
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'org_id': self.org_id,
            'name': self.name,
            'email': self.email,
            'mobile': self.mobile,
            'nic_name': self.nic_name,
            'status': self.status,
            'trading_flag': self.trading_flag,
            'is_admin': self.is_admin,
            'is_super_admin': self.is_super_admin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
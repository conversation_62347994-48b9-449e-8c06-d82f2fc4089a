from services.auth_service import AlgoDeltaAuthService
import time
import json
from datetime import datetime, timedelta
from models.strategy_info import StrategyInfo, StrategyRecord, StrategyOrder
from extensions import db
from flask import current_app
from app import create_app
import logging
from pytz import timezone

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def get_strategies():
    app = create_app()
    max_retries = 5
    retry_count = 0
    backoff_time = 5
    max_backoff_time = 120
    last_login_time = None
    token_expiry = timedelta(hours=1)
    last_status_print = None
    status_print_interval = timedelta(minutes=5)

    with app.app_context():
        while True:
            auth_service = AlgoDeltaAuthService()
            current_time = datetime.now()
            try:
                # Check and refresh token if needed
                if not auth_service.auth_token or \
                       (last_login_time and current_time - last_login_time >= token_expiry) or \
                       (hasattr(auth_service, 'token_expiry') and auth_service.token_expiry and current_time >= auth_service.token_expiry):
                    logger.info('Token expired or missing - attempting to refresh/login')
                
                if hasattr(auth_service, 'refresh_token') and auth_service.refresh_token:
                    logger.info('Attempting token refresh')
                    success, result = auth_service.refresh_session()
                    if not success:
                        logger.warning('Token refresh failed, falling back to login')
                        success, result = auth_service.login()
                else:
                    success, result = auth_service.login()
                
                logger.info(f'Authentication result: {"success" if success else "failed"}')
                
                if not success:
                    error_msg = result.get('error', 'Unknown error')
                    logger.error(f'Authentication failed: {error_msg}')
                    if current_time - (last_status_print or datetime.min) >= status_print_interval:
                        logger.info(f'Retrying in {backoff_time} seconds...')
                        last_status_print = current_time
                    time.sleep(backoff_time)
                    backoff_time = min(backoff_time * 2, max_backoff_time)
                    continue
                
                last_login_time = current_time
                backoff_time = 5
                logger.info('Authentication successful - collecting strategy data')
                last_status_print = current_time

                # Fetch strategy data
                success, data = auth_service.make_api_request('GET', 'admin/getstrategies')
                
                if success and isinstance(data, dict) and 'data' in data and isinstance(data['data'], list):
                    retry_count = 0
                    backoff_time = 5
                    strategy_list = data['data']
                    
                    try:
                        # Delete old records before adding new ones
                        StrategyInfo.query.delete()
                        StrategyRecord.query.delete()
                        StrategyOrder.query.delete()
                        
                        for strategy in strategy_list:
                            strategy_info = StrategyInfo(
                                strategy_id=strategy.get('strategy_info', {}).get('id'),
                                org_id=strategy.get('strategy_info', {}).get('org_id'),
                                name=strategy.get('strategy_info', {}).get('name'),
                                description=strategy.get('strategy_info', {}).get('description'),
                                required_amount=strategy.get('strategy_info', {}).get('required_amount'),
                                master_broker_id=strategy.get('strategy_info', {}).get('master_broker_id'),
                                is_master_connected=strategy.get('strategy_info', {}).get('is_master_connected'),
                                place_rejected=strategy.get('strategy_info', {}).get('place_rejected'),
                                reverse_order=strategy.get('strategy_info', {}).get('reverse_order'),
                                is_private=strategy.get('strategy_info', {}).get('is_private'),
                                order_key=strategy.get('strategy_info', {}).get('order_key'),
                                order_ip=strategy.get('strategy_info', {}).get('order_ip'),
                                trading_flag=strategy.get('strategy_info', {}).get('trading_flag'),
                                status=strategy.get('strategy_info', {}).get('status'),
                                last_signal_id=strategy.get('strategy_info', {}).get('last_signal_id'),
                                admin_id=strategy.get('strategy_info', {}).get('admin_id'),
                                subscription_no=strategy.get('subscription_no'),
                                allow_user_no=strategy.get('allow_user_no'),
                                sys_flag=strategy.get('strategy_info', {}).get('sys_flag'),
                                add_time=datetime.fromisoformat(strategy.get('strategy_info', {}).get('add_time').replace('Z', '+00:00')) if strategy.get('strategy_info', {}).get('add_time') else None,
                                update_time=datetime.fromisoformat(strategy.get('strategy_info', {}).get('update_time').replace('Z', '+00:00')) if strategy.get('strategy_info', {}).get('update_time') else None
                            )
                            db.session.add(strategy_info)

                            # Add records
                            for record in strategy.get('records', []):
                                strategy_record = StrategyRecord(
                                    strategy_id=strategy_info.strategy_id,
                                    org_id=record.get('org_id'),
                                    pnl=record.get('pnl'),
                                    date=datetime.fromisoformat(record.get('date').replace('Z', '+00:00')) if record.get('date') else None,
                                    sys_flag=record.get('sys_flag')
                                )
                                db.session.add(strategy_record)

                            # Add orders
                            for order in strategy.get('orders', []):
                                strategy_order = StrategyOrder(
                                    strategy_id=strategy_info.strategy_id,
                                    is_ct=order.get('is_ct'),
                                    org_id=order.get('org_id'),
                                    ref_name=order.get('ref_name'),
                                    exchange=order.get('exchange'),
                                    quantity=order.get('quantity'),
                                    price=order.get('price'),
                                    action_type=order.get('action_type'),
                                    order_time=datetime.fromisoformat(order.get('order_time').replace('Z', '+00:00')) if order.get('order_time') else None,
                                    status=order.get('status'),
                                    emsg=order.get('emsg'),
                                    sys_flag=order.get('sys_flag'),
                                    trading_symbol=order.get('trading_symbol'),
                                    order_from=order.get('order_from')
                                )
                                db.session.add(strategy_order)
                        db.session.commit()
                        logger.info(f'Successfully saved {len(strategy_list)} strategy records')
                        return True
                    except Exception as e:
                        logger.error(f'Database error: {str(e)}')
                        db.session.rollback()
                        return False
                elif isinstance(data, dict) and data.get('error', '').startswith('401'):
                    logger.info('Token expired, initiating re-login...')
                    last_login_time = None
                    return False
                else:
                    retry_count += 1
                    error_msg = data.get("error", "Unknown error") if isinstance(data, dict) else "Unknown error"
                    logger.warning(f'Failed to fetch strategies (attempt {retry_count}/{max_retries}) - {error_msg}')
                    
                    if retry_count >= max_retries:
                        logger.warning('Max retries reached, exiting...')
                        return False
                    else:
                        logger.info(f'Retrying in {backoff_time} seconds...')
                        time.sleep(backoff_time)
                        backoff_time = min(backoff_time * 2, max_backoff_time)
                        continue
            except Exception as e:
                logger.error(f'Unexpected error: {str(e)}')
                return False

if __name__ == '__main__':
    try:
        success = get_strategies()
        if success:
            print('\nStrategy data fetch completed successfully.')
        else:
            print('\nStrategy data fetch failed.')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
@echo off
echo Starting AlgoFactory Dashboard...
echo.

REM Check if UV is installed
uv --version >nul 2>&1
if %errorlevel% neq 0 (
    echo UV is not installed. Please install UV first:
    echo powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
    pause
    exit /b 1
)

REM Install dependencies if needed
echo Installing dependencies...
uv sync

REM Run the dashboard
echo.
echo Starting Streamlit dashboard...
echo Dashboard will open at: http://localhost:8501
echo.
echo Login credentials:
echo Email: <EMAIL>
echo Password: admin123
echo.
uv run streamlit run app.py

pause

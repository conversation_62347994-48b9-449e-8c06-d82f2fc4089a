from flask import Flask, jsonify, request
from extensions import db, login_manager
from models.models import User
from dotenv import load_dotenv
import os
import multiprocessing
import signal
import sys

# Load environment variables
load_dotenv()

def create_app():
    app = Flask(__name__)

    # Configure app
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///algodelta.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Register blueprints
    from routes.auth import auth_bp
    from routes.api import api_bp
    app.register_blueprint(auth_bp)
    app.register_blueprint(api_bp)

    @app.route('/')
    def index():
        from flask_login import current_user
        if current_user.is_authenticated:
            return jsonify({
                'message': 'Welcome to AlgoDelta Admin API',
                'user': {
                    'email': current_user.email,
                    'domain_name': current_user.domain_name
                },
                'endpoints': {
                    'api_data': '/api/data',
                    'api_logs': '/api/logs',
                    'status': '/auth/status',
                    'logout': '/auth/logout'
                }
            })
        return jsonify({
            'message': 'Welcome to AlgoDelta Admin API',
            'endpoints': {
                'login': '/auth/login'
            }
        })

    @app.errorhandler(404)
    def not_found_error(error):
        return jsonify({
            'error': 'Not found',
            'message': 'The requested URL was not found on the server.',
            'path': request.path,
            'method': request.method
        }), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

    return app

app = create_app()

broker_process = None

def run_broker_with_context():
    with app.app_context():
        from get_user_brokers import get_user_brokers
        get_user_brokers()

def start_broker_process():
    global broker_process
    broker_process = multiprocessing.Process(target=run_broker_with_context)
    broker_process.start()
    print('Broker data fetch process started')

def stop_broker_process(signum=None, frame=None):
    global broker_process
    if broker_process:
        broker_process.terminate()
        broker_process.join()
        print('\nBroker data fetch process stopped')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    
    # Start broker data fetch process
    start_broker_process()
    
    # Register signal handler for graceful shutdown
    signal.signal(signal.SIGINT, stop_broker_process)
    signal.signal(signal.SIGTERM, stop_broker_process)
    
    try:
        app.run(debug=True)
    finally:
        stop_broker_process()
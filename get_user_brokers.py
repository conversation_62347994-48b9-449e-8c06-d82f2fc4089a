from services.auth_service import AlgoDeltaAuthService
import time
import json
from datetime import datetime, timedelta
from models.broker_data import BrokerData
from extensions import db
from flask import current_app
from app import create_app
import logging
from pytz import timezone

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        # Convert timestamp to timezone-aware datetime in UTC
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        # Convert to IST
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging with IST timezone and prevent duplicate handlers
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []  # Clear any existing handlers

# Add a single stream handler with IST formatter
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)

# Prevent the logger from propagating messages to the root logger
logger.propagate = False

def get_user_brokers():
    app = create_app()
    last_login_time = None
    token_expiry = timedelta(hours=1)  # Match token expiry with auth service
    last_status_print = None
    status_print_interval = timedelta(minutes=5)  # Print status every 5 minutes
    max_retries = 5  # Maximum retries for API requests
    retry_count = 0
    backoff_time = 5  # Initial backoff time in seconds
    max_backoff_time = 120  # Maximum backoff time in seconds

    with app.app_context():
        auth_service = AlgoDeltaAuthService()
        while True:
            current_time = datetime.now()
            try:
                # Check and refresh token if needed
                if not auth_service.auth_token or \
                   (last_login_time and current_time - last_login_time >= token_expiry) or \
                   (hasattr(auth_service, 'token_expiry') and auth_service.token_expiry and current_time >= auth_service.token_expiry):
                    logger.info('Token expired or missing - attempting to refresh/login')
                    
                    # Try token refresh first if we have a refresh token
                    if hasattr(auth_service, 'refresh_token') and auth_service.refresh_token:
                        logger.info('Attempting token refresh')
                        success, result = auth_service.refresh_session()
                        if not success:
                            logger.warning('Token refresh failed, falling back to login')
                            success, result = auth_service.login()
                    else:
                        success, result = auth_service.login()
                    
                    logger.info(f'Authentication result: {"success" if success else "failed"}')
                    
                    if not success:
                        error_msg = result.get('error', 'Unknown error')
                        logger.error(f'Authentication failed: {error_msg}')
                        if current_time - (last_status_print or datetime.min) >= status_print_interval:
                            logger.info(f'Retrying in {backoff_time} seconds...')
                            last_status_print = current_time
                        time.sleep(backoff_time)
                        backoff_time = min(backoff_time * 2, max_backoff_time)
                        continue
                    
                    last_login_time = current_time
                    backoff_time = 5  # Reset backoff time after successful login
                    logger.info('Authentication successful - collecting broker data')
                    last_status_print = current_time

                # Fetch broker data
                success, data = auth_service.make_api_request('GET', 'admin/getuserbrokers')
                
                if success and isinstance(data, dict) and 'data' in data and isinstance(data['data'], list):
                    retry_count = 0  # Reset retry counter on success
                    backoff_time = 5  # Reset backoff time on success
                    broker_list = data['data']
                    
                    try:
                        # Delete old records before adding new ones
                        BrokerData.query.delete()
                        
                        for broker in broker_list:
                            # Safe type conversion with error handling
                            try:
                                pnl = float(broker.get('pnl', 0.0))
                            except (ValueError, TypeError):
                                pnl = 0.0
                                
                            try:
                                positions = int(broker.get('positions', 0))
                            except (ValueError, TypeError):
                                positions = 0
                                
                            try:
                                margin = float(broker.get('margin', 0.0))
                            except (ValueError, TypeError):
                                margin = 0.0
                                
                            try:
                                parent_id = int(broker.get('parent_id')) if broker.get('parent_id') else None
                            except (ValueError, TypeError):
                                parent_id = None
                                
                            broker_data = BrokerData(
                                broker_id=str(broker.get('id', '')),
                                broker_name=broker.get('broker_name', ''),
                                user_id=str(broker.get('parent_id', '')),
                                user_name=broker.get('nic_name', ''),
                                trading_flag=bool(broker.get('trading_flag', False)),
                                broker_username=broker.get('broker_username', ''),
                                parent_id=parent_id,
                                nic_name=broker.get('nic_name', ''),
                                demat_name=broker.get('demat_name', ''),
                                broker_flag=bool(broker.get('broker_flag', False)),
                                pnl=pnl,
                                positions=positions,
                                margin=margin,
                                broker_data=broker
                            )
                            db.session.add(broker_data)
                        db.session.commit()
                        logger.info(f'Successfully saved {len(broker_list)} broker records')
                        time.sleep(1)  # Wait for 1 second before next fetch on success
                    except Exception as e:
                        logger.error(f'Database error: {str(e)}')
                        db.session.rollback()
                        time.sleep(5)  # Short delay after database error
                        continue  # Continue to next iteration after database error
                    
                elif isinstance(data, dict) and data.get('error', '').startswith('401'):
                    logger.info('Token expired, initiating re-login...')
                    last_login_time = None  # Force re-login on next iteration
                    retry_count = 0  # Reset retry counter for new login attempt
                    backoff_time = 5  # Reset backoff time
                    continue  # Skip to next iteration to trigger re-login
                
                else:
                    retry_count += 1
                    error_msg = data.get("error", "Unknown error") if isinstance(data, dict) else "Unknown error"
                    logger.warning(f'Failed to fetch brokers (attempt {retry_count}/{max_retries}) - {error_msg}')
                    
                    if retry_count >= max_retries:
                        logger.warning(f'Max retries reached, waiting {backoff_time} seconds before next attempt...')
                        time.sleep(backoff_time)
                        backoff_time = min(backoff_time * 2, max_backoff_time)
                        retry_count = 0  # Reset retry counter after waiting
                    else:
                        logger.info(f'Retrying in {backoff_time} seconds...')
                        time.sleep(backoff_time)  # Wait before retry
                        backoff_time = min(backoff_time * 2, max_backoff_time)
                    continue  # Skip to next iteration after handling retry

            except Exception as e:
                logger.error(f'Unexpected error in main loop: {str(e)}')
                time.sleep(backoff_time)
                backoff_time = min(backoff_time * 2, max_backoff_time)

if __name__ == '__main__':
    try:
        get_user_brokers()
    except KeyboardInterrupt:
        print('\nStopping broker data fetch...')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
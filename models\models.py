from datetime import datetime
from flask_login import UserMixin
from extensions import db
from pytz import timezone

ist = timezone('Asia/Kolkata')

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    domain_name = db.Column(db.String(120))
    auth_token = db.Column(db.Text)
    refresh_token = db.Column(db.Text)
    last_login = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(ist), onupdate=lambda: datetime.now(ist))

class ApiData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    endpoint = db.Column(db.String(255), nullable=False)
    response_data = db.Column(db.JSON)
    status = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    user = db.relationship('User', backref=db.backref('api_data', lazy=True))

class ApiLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    method = db.Column(db.String(10), nullable=False)
    endpoint = db.Column(db.String(255), nullable=False)
    request_data = db.Column(db.JSON)
    response_data = db.Column(db.JSON)
    status_code = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    user = db.relationship('User', backref=db.backref('api_logs', lazy=True))


class TokenStore(db.Model):
    __tablename__ = 'token_store'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True, nullable=False)
    auth_token = db.Column(db.Text, nullable=True)
    refresh_token = db.Column(db.Text, nullable=True)
    token_expiry = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(ist), onupdate=lambda: datetime.now(ist))
    
    def __repr__(self):
        return f'<TokenStore {self.email}>'
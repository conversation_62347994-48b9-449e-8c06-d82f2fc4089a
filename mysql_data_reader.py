import mysql.connector
import logging
from datetime import datetime
from pytz import timezone

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def get_user_trading_params(email):
    from mysql_connection_pool import MySQLConnectionPool
    
    try:
        # Get connection from pool
        connection = MySQLConnectionPool.get_instance().get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # Query to fetch trading parameters for the specified email
        query = """SELECT target_amount, stoploss_amount, trail_by, lock_profit 
                  FROM users WHERE email = %s"""
        cursor.execute(query, (email,))
        result = cursor.fetchone()
        
        if result:
            return {
                'target_amount': float(result['target_amount']) if result['target_amount'] else 500,
                'stoploss_amount': float(result['stoploss_amount']) if result['stoploss_amount'] else -500,
                'trail_by': float(result['trail_by']) if result['trail_by'] else 0,
                'lock_profit': float(result['lock_profit']) if result['lock_profit'] else 0
            }
        else:
            logger.warning(f'No trading parameters found for email: {email}')
            return {
                'target_amount': 500,
                'stoploss_amount': -500,
                'trail_by': 0,
                'lock_profit': 0
            }
            
    except mysql.connector.Error as err:
        logger.error(f'MySQL Error: {err}')
        return None
    except Exception as e:
        logger.error(f'Unexpected error: {str(e)}')
        return None
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals() and connection.is_connected():
            connection.close()
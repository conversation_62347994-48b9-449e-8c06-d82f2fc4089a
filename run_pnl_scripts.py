import subprocess
import sys
import os

def run_scripts():
    # Get the directory of the current script
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Define the scripts to run
    scripts = [
        'pnl_squeroff_609_Banish.py',
        'pnl_squeroff_2392_Csk.py',
        'pnl_squeroff_2931_Suhash.py'
    ]
    
    # Create a list to store the processes
    processes = []
    
    try:
        # Start each script in a separate process
        for script in scripts:
            script_path = os.path.join(current_dir, script)
            if os.path.exists(script_path):
                # Start the process with a new console window
                process = subprocess.Popen(
                    [sys.executable, script_path],
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )
                processes.append(process)
                print(f'Started {script}')
            else:
                print(f'Script not found: {script}')
        
        # Wait for user input to terminate
        input('Press Enter to terminate all scripts...')
        
        # Terminate all processes
        for process in processes:
            process.terminate()
        
        print('All scripts terminated')
        
    except KeyboardInterrupt:
        print('\nTerminating all scripts...')
        for process in processes:
            process.terminate()
        print('All scripts terminated')
    except Exception as e:
        print(f'Error: {str(e)}')
        for process in processes:
            process.terminate()
        print('All scripts terminated due to error')

if __name__ == '__main__':
    run_scripts()
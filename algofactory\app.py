import streamlit as st
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple

# Page configuration
st.set_page_config(
    page_title="AlgoFactory - Strategies",
    page_icon="📈",
    layout="wide"
)

class AlgoDeltaAuthService:
    """Simple AlgoDelta API Service"""
    BASE_URL = "https://bpapil1.algodelta.com/api/v1"

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        self.auth_token = None

    def login(self, email: str, password: str, domain_name: str = "algofactory.in") -> Tuple[bool, str]:
        """Login to AlgoDelta API"""
        url = f"{self.BASE_URL}/auth/login"
        payload = {"email": email, "password": password, "domain_name": domain_name}

        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()
            session_data = response.json()

            if 'token' in session_data:
                self.auth_token = session_data['token']
            elif 'data' in session_data and 'token' in session_data['data']:
                self.auth_token = session_data['data']['token']
            else:
                return False, 'Invalid response structure'

            self.session.headers.update({'Authorization': self.auth_token})
            return True, 'Login successful'

        except Exception as e:
            return False, str(e)

    def get_strategies(self) -> Tuple[bool, Dict]:
        """Get strategies from AlgoDelta API"""
        url = f"{self.BASE_URL}/admin/getstrategies"
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            return True, response.json()
        except Exception as e:
            return False, {'error': str(e)}

# Initialize session state
if 'authenticated' not in st.session_state:
    st.session_state['authenticated'] = False
if 'auth_service' not in st.session_state:
    st.session_state['auth_service'] = None

def login_page():
    """Simple login page"""
    st.title("🏭 AlgoFactory - Strategies Dashboard")

    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.subheader("🔐 Login")

        with st.form("login_form"):
            email = st.text_input("Email", placeholder="Enter your AlgoDelta email")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            domain_name = st.text_input("Domain", value="algofactory.in")

            if st.form_submit_button("Login", use_container_width=True):
                if email and password and domain_name:
                    with st.spinner("Authenticating..."):
                        auth_service = AlgoDeltaAuthService()
                        success, message = auth_service.login(email, password, domain_name)

                        if success:
                            st.session_state['authenticated'] = True
                            st.session_state['auth_service'] = auth_service
                            st.success("Login successful!")
                            st.rerun()
                        else:
                            st.error(f"Login failed: {message}")
                else:
                    st.error("Please fill all fields")

def strategies_page():
    """Display strategies page"""
    # Header with logout
    col1, col2 = st.columns([4, 1])
    with col1:
        st.title("📈 AlgoFactory - Strategies")
    with col2:
        if st.button("Logout"):
            st.session_state['authenticated'] = False
            st.session_state['auth_service'] = None
            st.rerun()

    st.markdown("---")

    # Get strategies
    auth_service = st.session_state['auth_service']

    with st.spinner("Loading strategies..."):
        success, data = auth_service.get_strategies()

    if not success:
        st.error(f"Failed to load strategies: {data.get('error', 'Unknown error')}")
        return

    if 'data' not in data or not data['data']:
        st.warning("No strategies found")
        return

    strategies = data['data']
    st.success(f"Found {len(strategies)} strategies")

    # Display strategies in a clean table format
    strategy_data = []
    for strategy in strategies:
        strategy_data.append({
            'ID': strategy.get('id', 'N/A'),
            'Name': strategy.get('strategy_name', 'N/A'),
            'Status': '🟢 Active' if strategy.get('trading_flag') else '🔴 Inactive',
            'Users': strategy.get('user_count', 0),
            'Created': strategy.get('created_at', 'N/A')[:10] if strategy.get('created_at') else 'N/A'
        })

    # Convert to DataFrame and display
    df = pd.DataFrame(strategy_data)
    st.dataframe(df, use_container_width=True, hide_index=True)

def show_dashboard_content():
    """Show dashboard overview content"""
    st.subheader("📊 Dashboard Overview")
    
    # Metrics row
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Active Strategies", "12", "2")
    with col2:
        st.metric("Total Users", "45", "5")
    with col3:
        st.metric("Today's PnL", "₹25,430", "12%")
    with col4:
        st.metric("Success Rate", "78%", "3%")
    
    st.markdown("---")
    
    # Charts placeholder
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 Performance Chart")
        st.info("Performance charts will be implemented here")
        
    with col2:
        st.subheader("📊 Strategy Distribution")
        st.info("Strategy distribution charts will be implemented here")

def fetch_strategies():
    """Fetch strategies from AlgoDelta API"""
    if 'auth_service' not in st.session_state or st.session_state['auth_service'] is None:
        return False, "Authentication service not available"

    auth_service = st.session_state['auth_service']
    success, data = auth_service.make_api_request('GET', 'admin/getstrategies')

    if success:
        st.session_state['strategies_data'] = data
        return True, data
    else:
        return False, data.get('error', 'Failed to fetch strategies')

def show_strategies_content():
    """Show strategies management content"""
    st.subheader("📈 Strategies Management")

    # Refresh button
    col1, col2 = st.columns([1, 4])
    with col1:
        if st.button("🔄 Refresh", type="secondary"):
            st.session_state['strategies_data'] = None
            st.rerun()

    st.markdown("---")

    # Fetch strategies if not already loaded
    if st.session_state['strategies_data'] is None:
        with st.spinner("Fetching strategies from AlgoDelta..."):
            success, result = fetch_strategies()
            if not success:
                st.error(f"Failed to fetch strategies: {result}")
                return

    # Display strategies
    strategies_data = st.session_state['strategies_data']

    if not strategies_data or 'data' not in strategies_data:
        st.warning("No strategies data available")
        return

    strategies = strategies_data['data']

    if not strategies:
        st.info("No strategies found")
        return

    st.success(f"Found {len(strategies)} strategies")

    # Display strategies in cards
    for strategy in strategies:
        with st.container():
            st.markdown('<div class="strategy-card">', unsafe_allow_html=True)

            # Strategy header
            col1, col2, col3 = st.columns([3, 1, 1])

            with col1:
                strategy_id = strategy.get('id', 'N/A')
                strategy_name = strategy.get('strategy_name', f'Strategy {strategy_id}')
                st.markdown(f"### 🎯 ID: **{strategy_id}** | Name: **{strategy_name}**")
                if 'description' in strategy and strategy['description']:
                    st.caption(f"📝 {strategy['description']}")

            with col2:
                trading_flag = strategy.get('trading_flag', False)
                if trading_flag:
                    st.write("� Active")
                else:
                    st.write("🔴 Inactive")

            with col3:
                if st.button("⚙️ Configure", key=f"config_{strategy_id}", type="primary"):
                    show_strategy_config(strategy)

            # Strategy details
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Strategy ID", strategy.get('id', 'N/A'))
            with col2:
                st.metric("Users", strategy.get('user_count', 0))
            with col3:
                created_at = strategy.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        formatted_date = date_obj.strftime('%Y-%m-%d')
                        st.metric("Created", formatted_date)
                    except:
                        st.metric("Created", created_at[:10] if len(created_at) >= 10 else created_at)
                else:
                    st.metric("Created", "N/A")
            with col4:
                updated_at = strategy.get('updated_at', '')
                if updated_at:
                    try:
                        date_obj = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        formatted_date = date_obj.strftime('%Y-%m-%d')
                        st.metric("Updated", formatted_date)
                    except:
                        st.metric("Updated", updated_at[:10] if len(updated_at) >= 10 else updated_at)
                else:
                    st.metric("Updated", "N/A")

            st.markdown('</div>', unsafe_allow_html=True)
            st.markdown("---")

def show_strategy_config(strategy):
    """Show strategy configuration modal"""
    strategy_name = strategy.get('strategy_name', f'Strategy {strategy.get("id", "Unknown")}')
    strategy_id = strategy.get('id')

    st.subheader(f"⚙️ Configure {strategy_name}")

    # Display current strategy information
    with st.expander("📊 Strategy Information", expanded=True):
        col1, col2, col3 = st.columns(3)
        with col1:
            st.write(f"**ID:** {strategy_id}")
            st.write(f"**Name:** {strategy_name}")
        with col2:
            st.write(f"**Status:** {'🟢 Active' if strategy.get('trading_flag') else '🔴 Inactive'}")
            st.write(f"**Users:** {strategy.get('user_count', 0)}")
        with col3:
            created_at = strategy.get('created_at', '')
            if created_at:
                try:
                    date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_date = date_obj.strftime('%Y-%m-%d %H:%M')
                    st.write(f"**Created:** {formatted_date}")
                except:
                    st.write(f"**Created:** {created_at}")

    st.markdown("---")

    # Configuration form
    st.subheader("🎯 Risk Management Settings")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Stop Loss & Target**")
        stop_loss = st.number_input(
            "Stop Loss (%)",
            min_value=0.1,
            max_value=10.0,
            value=2.0,
            step=0.1,
            help="Maximum loss percentage before closing position"
        )
        target = st.number_input(
            "Target (%)",
            min_value=0.1,
            max_value=20.0,
            value=5.0,
            step=0.1,
            help="Target profit percentage"
        )

    with col2:
        st.markdown("**Position Management**")
        risk_level = st.selectbox(
            "Risk Level",
            ["Low", "Medium", "High"],
            help="Overall risk tolerance for this strategy"
        )
        max_position = st.number_input(
            "Max Position Size (₹)",
            min_value=1000,
            max_value=1000000,
            value=50000,
            step=5000,
            help="Maximum position size in rupees"
        )

    # Additional settings
    st.markdown("**Advanced Settings**")
    col1, col2 = st.columns(2)

    with col1:
        trailing_sl = st.checkbox("Enable Trailing Stop Loss")
        auto_square_off = st.checkbox("Auto Square Off at 3:20 PM", value=True)

    with col2:
        max_trades_per_day = st.number_input("Max Trades Per Day", min_value=1, max_value=50, value=5)
        cooldown_period = st.number_input("Cooldown Period (minutes)", min_value=0, max_value=60, value=5)

    st.markdown("---")

    # Action buttons
    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        if st.button("💾 Save Configuration", type="primary", use_container_width=True):
            # Here you would typically make an API call to save the configuration
            st.success("Configuration saved successfully!")
            st.info("Note: API integration for saving configuration will be implemented")

    with col2:
        if st.button("🔄 Reset to Default", type="secondary", use_container_width=True):
            st.warning("Configuration reset to default values")
            st.rerun()

    with col3:
        if st.button("❌ Cancel", use_container_width=True):
            st.info("Configuration cancelled")
            st.rerun()

def show_users_content():
    """Show users management content"""
    st.subheader("👥 Users Management")
    st.info("Users management functionality will be implemented here")

def show_settings_content():
    """Show settings content"""
    st.subheader("⚙️ Settings")
    st.info("Settings functionality will be implemented here")

# Main application logic
def main():
    if st.session_state['authentication_status'] is None:
        login_page()
    else:
        dashboard_page()

if __name__ == "__main__":
    main()

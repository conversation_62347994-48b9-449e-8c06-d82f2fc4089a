# AlgoFactory Admin Dashboard

A comprehensive Streamlit-based admin dashboard for managing trading strategies, users, and system configurations.

## Features

- 🔐 **Secure Login System** - Email/password authentication
- 📊 **Dashboard Overview** - Key metrics and performance charts
- 📈 **Strategy Management** - Configure stop-loss, targets, and risk parameters
- 👥 **User Management** - Manage system users and permissions
- ⚙️ **Settings** - System configuration options

## Quick Start with UV

### Prerequisites
- Python 3.8 or higher
- UV package manager

### Installation

1. **Install UV** (if not already installed):
   ```bash
   # On Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   
   # On macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **Navigate to the project directory**:
   ```bash
   cd algofactory
   ```

3. **Install dependencies using UV**:
   ```bash
   uv sync
   ```

4. **Run the application**:
   ```bash
   uv run streamlit run app.py
   ```

### Alternative Installation (without UV)

If you prefer using pip:

```bash
pip install -r requirements.txt
streamlit run app.py
```

## Usage

1. **Access the Dashboard**:
   - Open your browser and go to `http://localhost:8501`

2. **Login Credentials**:
   - Use your AlgoDelta email and password
   - Domain: `algofactory.in` (default)
   - The system authenticates directly with AlgoDelta API

3. **Navigation**:
   - Use the sidebar to navigate between different sections
   - Dashboard: Overview and metrics
   - Strategies: Manage trading strategies with stop-loss/target settings
   - Users: User management (coming soon)
   - Settings: System settings (coming soon)

## Project Structure

```
algofactory/
├── app.py              # Main Streamlit application
├── requirements.txt    # Python dependencies
├── pyproject.toml     # UV/Python project configuration
├── run_dashboard.bat  # Windows batch file to run dashboard
└── README.md          # This file
```

## Configuration

### Adding New Users

Currently, users are stored in the `users_db` dictionary in `app.py`. In production, this should be moved to a secure database.

### Strategy Configuration

Each strategy can be configured with:
- Stop Loss percentage
- Target percentage  
- Risk level (Low/Medium/High)
- Maximum position size

## Development

### Running in Development Mode

```bash
uv run streamlit run app.py --server.runOnSave true
```

This enables auto-reload when files are changed.

### Adding New Dependencies

```bash
uv add package-name
```

## Security Notes

- Default passwords are hashed using bcrypt
- In production, move user credentials to a secure database
- Consider implementing JWT tokens for session management
- Add HTTPS in production deployment

## Future Enhancements

- [ ] Database integration for user management
- [ ] Real-time strategy performance monitoring
- [ ] Advanced charting and analytics
- [ ] Email notifications for strategy alerts
- [ ] API integration for live trading data
- [ ] Role-based access control
- [ ] Audit logging

## Support

For support and questions, contact the AlgoFactory team.

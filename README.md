# AlgoDelta Admin Backend

A Flask-based admin backend for managing AlgoDelta API interactions and data storage.

## Features

- Seamless integration with AlgoDelta API
- User authentication and session management
- API request proxying and response storage
- API call logging and monitoring
- Database storage for API responses

## Setup

1. Create a virtual environment:
```bash
python -m venv venv
.\venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Create a `.env` file with the following variables:
```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///algodelta.db
```

4. Initialize the database:
```bash
python app.py
```

## Usage

1. Start the Flask server:
```bash
python app.py
```

2. Available endpoints:

### Authentication
- POST `/login` - Login with AlgoDelta credentials
- GET `/logout` - Logout current user
- GET `/refresh-token` - Refresh authentication token
- GET `/status` - Check authentication status

### API
- ANY `/proxy/<endpoint>` - Proxy requests to AlgoDelta API
- GET `/data` - Retrieve stored API responses
- GET `/logs` - View API call logs

## Query Parameters

For `/data` and `/logs` endpoints:
- `endpoint` - Filter by specific API endpoint
- `start_date` - Filter from start date
- `end_date` - Filter until end date

## Example Usage

```python
# Login
curl -X POST http://localhost:5000/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your-password","domain_name":"algofactory.in"}'

# Make API request
curl http://localhost:5000/proxy/some/endpoint \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token"
```
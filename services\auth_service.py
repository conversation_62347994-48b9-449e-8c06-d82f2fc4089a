import requests
import json
import os
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from flask import current_app
from datetime import datetime, timedelta
from models.models import TokenStore
from extensions import db

class AlgoDeltaAuthService:
    BASE_URL = "https://bpapil1.algodelta.com/api/v1"
    
    def __init__(self, email: str = "<EMAIL>", password: str = "Ap9211###", domain_name: str = "algofactory.in"):
        self.email = email
        self.password = password
        self.domain_name = domain_name
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self._load_tokens_from_db()
        self._setup_token_refresh()
    
    def _load_tokens_from_db(self):
        """Load tokens from database if available."""
        stored_tokens = TokenStore.query.filter_by(email=self.email).first()
        if stored_tokens and stored_tokens.token_expiry > datetime.now():
            self.auth_token = stored_tokens.auth_token
            self.refresh_token = stored_tokens.refresh_token
            self.token_expiry = stored_tokens.token_expiry
            self.session.headers.update({
                'Authorization': self.auth_token
            })
        else:
            self.auth_token = None
            self.refresh_token = None
            self.token_expiry = None
    
    def _save_tokens_to_db(self):
        """Save or update tokens in database."""
        stored_tokens = TokenStore.query.filter_by(email=self.email).first()
        if stored_tokens:
            stored_tokens.auth_token = self.auth_token
            stored_tokens.refresh_token = self.refresh_token
            stored_tokens.token_expiry = self.token_expiry
        else:
            new_tokens = TokenStore(
                email=self.email,
                auth_token=self.auth_token,
                refresh_token=self.refresh_token,
                token_expiry=self.token_expiry
            )
            db.session.add(new_tokens)
        db.session.commit()
    
    def _setup_token_refresh(self):
        """Setup automatic token refresh before expiration."""
        if not self.token_expiry:
            return
        
        # Calculate time until token expires (assuming 5 minutes before actual expiry)
        refresh_time = self.token_expiry - timedelta(minutes=5)
        if refresh_time > datetime.now():
            # Schedule token refresh
            self.refresh_session()
    
    def login(self) -> Tuple[bool, Dict]:
        """Login to AlgoDelta API and get authentication tokens."""
        url = f"{self.BASE_URL}/auth/login"
        payload = {
            "email": self.email,
            "password": self.password,
            "domain_name": self.domain_name
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            session_data = response.json()
            
            if 'token' in session_data:
                self.auth_token = session_data['token']
                self.refresh_token = session_data.get('refreshToken', '')
                self.token_expiry = datetime.now() + timedelta(hours=1)
            elif 'data' in session_data and 'token' in session_data['data']:
                self.auth_token = session_data['data']['token']
                self.refresh_token = session_data['data'].get('refreshToken', '')
                self.token_expiry = datetime.now() + timedelta(hours=1)
            else:
                return False, {'error': 'Invalid response structure'}
            
            self.session.headers.update({
                'Authorization': self.auth_token
            })
            
            # Save tokens to database
            self._save_tokens_to_db()
            
            # Setup automatic token refresh
            self._setup_token_refresh()
            
            return True, {
                'auth_token': self.auth_token,
                'refresh_token': self.refresh_token,
                'token_expiry': self.token_expiry
            }
            
        except requests.exceptions.RequestException as e:
            return False, {'error': str(e)}
        except Exception as e:
            return False, {'error': str(e)}
    
    def refresh_session(self) -> Tuple[bool, Dict]:
        """Refresh the authentication token using refresh token."""
        if not self.refresh_token:
            return False, {'error': 'No refresh token available'}
            
        url = f"{self.BASE_URL}/auth/refresh"
        try:
            response = self.session.post(url, headers={'Authorization': f'Bearer {self.refresh_token}'}, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if 'token' in data:
                self.auth_token = data['token']
                self.refresh_token = data.get('refreshToken', self.refresh_token)  # Update refresh token if provided
                self.token_expiry = datetime.now() + timedelta(hours=1)
                self.session.headers.update({
                    'Authorization': self.auth_token
                })
                # Update tokens in database
                self._save_tokens_to_db()
                return True, {'auth_token': self.auth_token}
            return False, {'error': 'Invalid response structure'}
            
        except requests.exceptions.Timeout:
            return False, {'error': 'Token refresh request timed out'}
        except requests.exceptions.RequestException as e:
            return False, {'error': f'Token refresh failed: {str(e)}'}
        except Exception as e:
            return False, {'error': f'Unexpected error during token refresh: {str(e)}'}
    
    def get_tokens(self) -> Dict:
        """Get the current authentication tokens."""
        return {
            'auth_token': self.auth_token,
            'refresh_token': self.refresh_token,
            'token_expiry': self.token_expiry
        }

    def make_api_request(self, method: str, endpoint: str, data: Dict = None) -> Tuple[bool, Dict]:
        """Make an authenticated API request to AlgoDelta."""
        url = f"{self.BASE_URL}/{endpoint.lstrip('/')}"
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                response = self.session.request(method, url, json=data, timeout=30)
                
                if response.status_code == 401:
                    # Token might be expired, try to refresh
                    success, refresh_result = self.refresh_session()
                    if success:
                        # Retry the request with new token
                        response = self.session.request(method, url, json=data, timeout=30)
                    else:
                        # If refresh failed, try to login again
                        login_success, login_result = self.login()
                        if login_success:
                            response = self.session.request(method, url, json=data, timeout=30)
                        else:
                            return False, {'error': f'Authentication failed: {login_result.get("error", "Unknown error")}'}
                
                try:
                    response.raise_for_status()
                    response_data = response.json()
                    if not response_data:
                        return False, {'error': 'Empty response from server'}
                    return True, response_data
                except requests.exceptions.JSONDecodeError:
                    return False, {'error': f'Invalid JSON response from server (Status: {response.status_code})'}
                except requests.exceptions.HTTPError:
                    error_detail = response.json() if response.text else {'message': 'No error details available'}
                    if response.status_code >= 500:
                        # Retry on server errors
                        retry_count += 1
                        if retry_count < max_retries:
                            continue
                    return False, {'error': f'HTTP {response.status_code}: {error_detail.get("message", str(error_detail))}'}
                
            except requests.exceptions.Timeout:
                retry_count += 1
                if retry_count < max_retries:
                    continue
                return False, {'error': 'Request timed out after multiple retries'}
            except requests.exceptions.ConnectionError as e:
                retry_count += 1
                if retry_count < max_retries:
                    continue
                return False, {'error': f'Connection error after multiple retries: {str(e)}'}
            except requests.exceptions.RequestException as e:
                return False, {'error': f'Request failed: {str(e)}'}
            except Exception as e:
                return False, {'error': f'Unexpected error: {str(e)}'}
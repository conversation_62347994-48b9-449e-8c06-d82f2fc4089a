from services.auth_service import AlgoDeltaAuthService
import time
import json
from datetime import datetime, timedelta
from models.user_data import UserData
from extensions import db
from flask import current_app
from app import create_app
import logging
from pytz import timezone

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def get_users():
    app = create_app()
    last_login_time = None
    token_expiry = timedelta(hours=1)
    last_status_print = None
    status_print_interval = timedelta(minutes=5)
    max_retries = 5
    retry_count = 0
    backoff_time = 5
    max_backoff_time = 120

    with app.app_context():
        auth_service = AlgoDeltaAuthService()
        current_time = datetime.now()
        try:
            # Check and refresh token if needed
            if not auth_service.auth_token or \
               (last_login_time and current_time - last_login_time >= token_expiry) or \
               (hasattr(auth_service, 'token_expiry') and auth_service.token_expiry and current_time >= auth_service.token_expiry):
                logger.info('Token expired or missing - attempting to refresh/login')
                
                # Try token refresh first if we have a refresh token
                if hasattr(auth_service, 'refresh_token') and auth_service.refresh_token:
                    logger.info('Attempting token refresh')
                    success, result = auth_service.refresh_session()
                    if not success:
                        logger.warning('Token refresh failed, falling back to login')
                        success, result = auth_service.login()
                else:
                    success, result = auth_service.login()
                
                logger.info(f'Authentication result: {"success" if success else "failed"}')
                
                if not success:
                    error_msg = result.get('error', 'Unknown error')
                    logger.error(f'Authentication failed: {error_msg}')
                    if current_time - (last_status_print or datetime.min) >= status_print_interval:
                        logger.info(f'Retrying in {backoff_time} seconds...')
                        last_status_print = current_time
                    time.sleep(backoff_time)
                    backoff_time = min(backoff_time * 2, max_backoff_time)
                    return False
                
                last_login_time = current_time
                backoff_time = 5
                logger.info('Authentication successful - collecting user data')
                last_status_print = current_time

            # Fetch user data
            success, data = auth_service.make_api_request('GET', 'admin/getusers')
            
            if success and isinstance(data, dict) and 'data' in data and isinstance(data['data'], list):
                retry_count = 0
                backoff_time = 5
                user_list = data['data']
                
                try:
                    # Delete old records before adding new ones
                    UserData.query.delete()
                    
                    for user in user_list:
                        user_data = UserData(
                            user_id=str(user.get('id', '')),
                            org_id=str(user.get('org_id', '')),
                            name=user.get('name', ''),
                            email=user.get('email', ''),
                            mobile=user.get('mobile', ''),
                            nic_name=user.get('nic_name', ''),
                            status=user.get('status', ''),
                            trading_flag=bool(user.get('trading_flag', False)),
                            is_admin=bool(user.get('is_admin', False)),
                            is_super_admin=bool(user.get('is_super_admin', False)),
                            user_data=user
                        )
                        db.session.add(user_data)
                    db.session.commit()
                    logger.info(f'Successfully saved {len(user_list)} user records')
                    time.sleep(1)
                except Exception as e:
                    logger.error(f'Database error: {str(e)}')
                    db.session.rollback()
                    time.sleep(5)
                    return False
                
            elif isinstance(data, dict) and data.get('error', '').startswith('401'):
                logger.info('Token expired, initiating re-login...')
                last_login_time = None
                retry_count = 0
                backoff_time = 5
                return False
            
            else:
                retry_count += 1
                error_msg = data.get("error", "Unknown error") if isinstance(data, dict) else "Unknown error"
                logger.warning(f'Failed to fetch users (attempt {retry_count}/{max_retries}) - {error_msg}')
                
                if retry_count >= max_retries:
                    logger.warning(f'Max retries reached, waiting {backoff_time} seconds before next attempt...')
                    time.sleep(backoff_time)
                    backoff_time = min(backoff_time * 2, max_backoff_time)
                    retry_count = 0
                else:
                    logger.info(f'Retrying in {backoff_time} seconds...')
                    time.sleep(backoff_time)
                    backoff_time = min(backoff_time * 2, max_backoff_time)
                return False

        except Exception as e:
            logger.error(f'Unexpected error: {str(e)}')
            time.sleep(backoff_time)
            return False
        
        return True

if __name__ == '__main__':
    try:
        success = get_users()
        if success:
            print('\nUser data fetch completed successfully.')
        else:
            print('\nUser data fetch failed.')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
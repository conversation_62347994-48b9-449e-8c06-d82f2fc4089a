import mysql.connector
from mysql.connector import pooling
import logging

# Configure logging
logger = logging.getLogger('mysql_connection_pool')
logger.setLevel(logging.INFO)

class MySQLConnectionPool:
    _instance = None
    _pool = None
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = MySQLConnectionPool()
        return cls._instance
    
    def __init__(self):
        if MySQLConnectionPool._instance is not None:
            raise Exception('This class is a singleton!')
        else:
            MySQLConnectionPool._instance = self
            self._initialize_pool()
    
    def _initialize_pool(self):
        dbconfig = {
            'host': 'srv1498.hstgr.io',
            'user': 'u352667016_algofactory',
            'password': 'Apapap92119211#',
            'database': 'u352667016_algofactory',
            'port': 3306
        }
        
        pool_config = {
            'pool_name': 'algofactory_pool',
            'pool_size': 5,  # Adjust based on your needs
            'pool_reset_session': True,
            **dbconfig
        }
        
        try:
            self._pool = mysql.connector.pooling.MySQLConnectionPool(**pool_config)
            logger.info('MySQL connection pool initialized successfully')
        except Exception as e:
            logger.error(f'Failed to initialize MySQL connection pool: {str(e)}')
            raise
    
    def get_connection(self):
        try:
            connection = self._pool.get_connection()
            return connection
        except Exception as e:
            logger.error(f'Failed to get connection from pool: {str(e)}')
            raise
    
    def close_all(self):
        # This method can be called when shutting down the application
        if self._pool:
            self._pool._remove_connections()
            logger.info('All pool connections closed')
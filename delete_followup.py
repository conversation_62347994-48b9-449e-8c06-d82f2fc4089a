from services.auth_service import AlgoDeltaAuthService
from app import create_app
import logging
from datetime import datetime, timedelta
from pytz import timezone

# Configure logging with IST timezone
ist = timezone('Asia/Kolkata')

# Create a custom formatter for IST timezone
class ISTFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        utc_dt = datetime.fromtimestamp(record.created, timezone('UTC'))
        ist_dt = utc_dt.astimezone(ist)
        if datefmt:
            return ist_dt.strftime(datefmt)
        return ist_dt.strftime('%Y-%m-%d %H:%M:%S %Z')

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = []

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
ist_formatter = ISTFormatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(ist_formatter)
logger.addHandler(console_handler)
logger.propagate = False

def delete_followups():
    app = create_app()
    last_login_time = None
    token_expiry = timedelta(hours=1)

    with app.app_context():
        auth_service = AlgoDeltaAuthService()
        current_time = datetime.now()
        try:
            # Check and refresh token if needed
            if not auth_service.auth_token or \
               (last_login_time and current_time - last_login_time >= token_expiry) or \
               (hasattr(auth_service, 'token_expiry') and auth_service.token_expiry and current_time >= auth_service.token_expiry):
                logger.info('Token expired or missing - attempting to refresh/login')
                
                # Try token refresh first if we have a refresh token
                if hasattr(auth_service, 'refresh_token') and auth_service.refresh_token:
                    logger.info('Attempting token refresh')
                    success, result = auth_service.refresh_session()
                    if not success:
                        logger.warning('Token refresh failed, falling back to login')
                        success, result = auth_service.login()
                else:
                    success, result = auth_service.login()
                
                if not success:
                    error_msg = result.get('error', 'Unknown error')
                    logger.error(f'Authentication failed: {error_msg}')
                    return False
                
                last_login_time = current_time

            # Get all followups
            success, data = auth_service.make_api_request('GET', 'admin/getfollowup')
            if not success or not isinstance(data, dict):
                logger.error('Failed to fetch followups')
                return False

            followups_to_delete = []
            protected_count = 0
            for followup in data.get('data', []):
                try:
                    followup_id = followup.get('id')
                    email = followup.get('email', '').lower()
                    if 'sharetradesoftware.com' not in email:
                        followups_to_delete.append(followup_id)
                    else:
                        protected_count += 1
                        logger.info(f'Skipping protected followup {followup_id} with email {email}')
                except (ValueError, TypeError):
                    continue

            logger.info(f'Found {len(followups_to_delete)} followups to delete and {protected_count} protected followups')

            # Delete followups
            deleted_count = 0
            for followup_id in followups_to_delete:
                try:
                    success, result = auth_service.make_api_request(
                        'POST',
                        'admin/deletefollowup',
                        data={'id': followup_id}
                    )

                    if success:
                        logger.info(f'Successfully deleted followup {followup_id}')
                        deleted_count += 1
                    else:
                        logger.warning(f'Failed to delete followup {followup_id}: {result.get("error", "Unknown error")}')

                except Exception as e:
                    logger.error(f'Error deleting followup {followup_id}: {str(e)}')
                    continue

            logger.info(f'Deletion complete. Successfully deleted {deleted_count} out of {len(followups_to_delete)} followups. Protected {protected_count} followups.')
            return True

        except Exception as e:
            logger.error(f'Unexpected error: {str(e)}')
            return False

if __name__ == '__main__':
    try:
        success = delete_followups()
        if success:
            print('\nFollowup deletion process completed.')
        else:
            print('\nFollowup deletion process failed.')
    except KeyboardInterrupt:
        print('\nOperation cancelled by user.')
    except Exception as e:
        print(f'Error: {str(e)}')
        raise
from datetime import datetime
from extensions import db
from pytz import timezone

ist = timezone('Asia/Kolkata')

class StrategyInfo(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    strategy_id = db.Column(db.Integer, nullable=False)
    org_id = db.Column(db.Integer)
    name = db.Column(db.String(255))
    description = db.Column(db.Text)
    required_amount = db.Column(db.Float)
    master_broker_id = db.Column(db.Integer)
    is_master_connected = db.Column(db.Boolean, default=False)
    place_rejected = db.Column(db.Boolean, default=False)
    reverse_order = db.Column(db.Boolean, default=False)
    is_private = db.Column(db.Bo<PERSON>an, default=True)
    order_key = db.Column(db.String(255))
    order_ip = db.Column(db.String(255))
    trading_flag = db.Column(db.Boolean, default=True)
    status = db.Column(db.String(50))
    last_signal_id = db.Column(db.Integer)
    admin_id = db.Column(db.Integer)
    subscription_no = db.Column(db.String(50))
    allow_user_no = db.Column(db.String(50))
    sys_flag = db.Column(db.Boolean, default=True)
    add_time = db.Column(db.DateTime)
    update_time = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(ist), onupdate=lambda: datetime.now(ist))

    # Relationships
    records = db.relationship('StrategyRecord', backref='strategy', lazy=True)
    orders = db.relationship('StrategyOrder', backref='strategy', lazy=True)

    def __repr__(self):
        return f'<StrategyInfo {self.name} (ID: {self.strategy_id})>'

class StrategyRecord(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    strategy_id = db.Column(db.Integer, db.ForeignKey('strategy_info.strategy_id'), nullable=False)
    org_id = db.Column(db.Integer)
    pnl = db.Column(db.Float)
    date = db.Column(db.DateTime)
    sys_flag = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(ist), onupdate=lambda: datetime.now(ist))

    def __repr__(self):
        return f'<StrategyRecord {self.id} for Strategy {self.strategy_id}>'

class StrategyOrder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    strategy_id = db.Column(db.Integer, db.ForeignKey('strategy_info.strategy_id'), nullable=False)
    is_ct = db.Column(db.Boolean, default=False)
    org_id = db.Column(db.Integer)
    ref_name = db.Column(db.String(255))
    exchange = db.Column(db.String(50))
    quantity = db.Column(db.Integer)
    price = db.Column(db.Float)
    action_type = db.Column(db.String(50))
    order_time = db.Column(db.DateTime)
    status = db.Column(db.Boolean, default=True)
    emsg = db.Column(db.Text)
    sys_flag = db.Column(db.Boolean, default=True)
    trading_symbol = db.Column(db.String(255))
    order_from = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(ist))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(ist), onupdate=lambda: datetime.now(ist))

    def __repr__(self):
        return f'<StrategyOrder {self.id} for Strategy {self.strategy_id}>'